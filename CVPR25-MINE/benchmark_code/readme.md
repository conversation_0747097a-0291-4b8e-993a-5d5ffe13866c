The benchmark code for MINE:Multimodal IntentioN and Emotion Understanding in the Wild.
---
Contains "MulT"\[1\], "MISA"\[2\] and "MPMM"\[3\].

Usage: python run.py --method [method_name]


Most of the code modified from MintRec \[4\], thanks!

\[1\] Y.-<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>, “Multimodal transformer for unaligned multimodal language sequences,” in Proceedings of the 57th Annual
Meeting of the Association for Computational Linguistics. 2019, pp. 6558–6569.

\[2\]<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, “Misa: Modality-invariant and-specific representations for multimodal sentiment analysis,” in Proceedings of the 28th ACM International Conference on Multimedia, 2020, pp. 1122–1131

\[3\]Y.-<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>, “Multimodal prompting with missing modalities for visual recognition,” in Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, 2023, pp. 14 943–14 952

\[4\] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, “Mintrec: A new dataset for multimodal intent recognition,” in Proceedings of the 30th ACM International Conference on Multimedia, 2022, pp. 1688–1697
