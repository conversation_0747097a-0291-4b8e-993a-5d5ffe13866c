benchmarks = {
    'MIntRec': {
        'intent_labels': [
            '<PERSON><PERSON><PERSON>', 'Praise', 'Apologise', 'Thank', 'Criticize',
            'Agree', 'Taunt', 'Flaunt',
            'Joke', 'Oppose',
                    'Comfort', 'Care', 'Inform', 'Advise', 'Arrange', 'Introduce', 'Leave',
                    'Prevent', 'Greet', 'Ask for help'
        ],
        'binary_maps': {
            'Complain': 'Emotion', 'Praise': 'Emotion', 'Apologise': 'Emotion', 'Thank': 'Emotion', 'Criticize': 'Emotion',
            'Care': 'Emotion', 'Agree': 'Emotion', 'Taunt': 'Emotion', 'Flaunt': 'Emotion',
                    'Joke': 'Emotion', 'Oppose': 'Emotion',
                    'Inform': 'Goal', 'Advise': 'Goal', 'Arrange': 'Goal', 'Introduce': 'Goal', 'Leave': 'Goal',
                    'Prevent': 'Goal', 'Greet': 'Goal', 'Ask for help': 'Goal', 'Comfort': 'Goal'
        },
        'binary_intent_labels': ['Emotion', 'Goal'],
        'max_seq_lengths': {
            'text': 30,  # truth: 26
            'video': 230,  # truth: 225
            'audio': 480,  # truth: 477
        },
        'feat_dims': {
            'text': 768,
            'video': 256,
            'audio': 768
        }
    },
    'MINE': {
        'goal_labels': [
            'Complain', 'Praise', 'Apologise', 'Thank', 'Criticize',
            'Agree', 'Taunt', 'Flaunt',
            'Joke', 'Oppose',
                    'Comfort', 'Care', 'Inform', 'Advise', 'Arrange', 'Introduce', 'Leave',
                    'Prevent', 'Greet', 'Ask for help', 'other'
        ],
        'emotion_labels': [
            'Complain', 'Praise', 'Apologise', 'Thank', 'Criticize',
            'Agree', 'Taunt', 'Flaunt',
            'Joke', 'Oppose',
                    'Comfort',
        ],
        'max_seq_lengths': {
            'text': 30,  # truth: 26
            'video': 230,  # truth: 225
            'audio': 480,  # truth: 477
        },
        'feat_dims': {
            'text': 768,
            'video': 256,
            'image': 256,
            'audio': 768
        }
    },
    'M3_emotion': {
        'emotion_labels': [
            'Complain', 'Praise', 'Apologise', 'Thank', 'Criticize',
            'Agree', 'Taunt', 'Flaunt',
            'Joke', 'Oppose',
                    'Comfort', 'Care', 'Inform', 'Advise', 'Arrange', 'Introduce', 'Leave',
                    'Prevent', 'Greet', 'Ask for help'
        ],
        'max_seq_lengths': {
            'text': 30,  # truth: 26
            'video': 230,  # truth: 225
            'audio': 480,  # truth: 477
        },
        'feat_dims': {
            'text': 768,
            'video': 256,
            'audio': 768
        }
    }
}
