2025-07-28 13:18:12,341 - ============================== Params ==============================
2025-07-28 13:18:12,341 - logger_name: mult_MINE_goal_2025-07-28-13-18-12
2025-07-28 13:18:12,341 - dataset: MINE
2025-07-28 13:18:12,341 - data_mode: goal
2025-07-28 13:18:12,341 - method: mult
2025-07-28 13:18:12,341 - text_backbone: bert-base-uncased
2025-07-28 13:18:12,341 - seed: 0
2025-07-28 13:18:12,341 - num_workers: 8
2025-07-28 13:18:12,341 - gpu_id: 0
2025-07-28 13:18:12,341 - data_path: ../mine-dataset
2025-07-28 13:18:12,341 - train: True
2025-07-28 13:18:12,341 - tune: False
2025-07-28 13:18:12,341 - save_model: True
2025-07-28 13:18:12,341 - save_results: True
2025-07-28 13:18:12,341 - log_path: logs
2025-07-28 13:18:12,341 - cache_path: cache
2025-07-28 13:18:12,341 - video_data_path: video_data
2025-07-28 13:18:12,341 - audio_data_path: audio_data
2025-07-28 13:18:12,341 - video_feats_path: video_feats.pkl
2025-07-28 13:18:12,341 - audio_feats_path: audio_feats.pkl
2025-07-28 13:18:12,341 - results_path: results
2025-07-28 13:18:12,342 - output_path: outputs
2025-07-28 13:18:12,342 - model_path: models
2025-07-28 13:18:12,342 - config_file_name: mult_bert
2025-07-28 13:18:12,342 - results_file_name: mult_bert.csv
2025-07-28 13:18:12,342 - padding_mode: zero
2025-07-28 13:18:12,342 - padding_loc: end
2025-07-28 13:18:12,342 - need_aligned: False
2025-07-28 13:18:12,342 - eval_monitor: f1
2025-07-28 13:18:12,342 - train_batch_size: 16
2025-07-28 13:18:12,342 - eval_batch_size: 8
2025-07-28 13:18:12,342 - test_batch_size: 8
2025-07-28 13:18:12,342 - wait_patience: 8
2025-07-28 13:18:12,342 - class_num_intent: 21
2025-07-28 13:18:12,342 - class_num_emotion: 11
2025-07-28 13:18:12,342 - num_train_epochs: 100
2025-07-28 13:18:12,342 - dst_feature_dims: 120
2025-07-28 13:18:12,342 - nheads: 8
2025-07-28 13:18:12,342 - n_levels: 8
2025-07-28 13:18:12,342 - attn_dropout: 0.0
2025-07-28 13:18:12,342 - attn_dropout_v: 0.2
2025-07-28 13:18:12,342 - attn_dropout_a: 0.2
2025-07-28 13:18:12,342 - relu_dropout: 0.0
2025-07-28 13:18:12,342 - embed_dropout: 0.1
2025-07-28 13:18:12,342 - res_dropout: 0.0
2025-07-28 13:18:12,342 - output_dropout: 0.2
2025-07-28 13:18:12,343 - text_dropout: 0.4
2025-07-28 13:18:12,343 - grad_clip: 0.5
2025-07-28 13:18:12,343 - attn_mask: True
2025-07-28 13:18:12,343 - conv1d_kernel_size_l: 5
2025-07-28 13:18:12,343 - conv1d_kernel_size_v: 1
2025-07-28 13:18:12,343 - conv1d_kernel_size_a: 1
2025-07-28 13:18:12,343 - lr: 3e-05
2025-07-28 13:18:12,343 - pred_output_path: outputs/mult_MINE_goal_2025-07-28-13-18-12
2025-07-28 13:18:12,343 - model_output_path: outputs/mult_MINE_goal_2025-07-28-13-18-12/models
2025-07-28 13:18:12,343 - ============================== End Params ==============================
2025-07-28 13:18:22,464 - Multimodal intent recognition begins...
2025-07-28 13:18:22,464 - Training begins...
2025-07-28 13:35:21,646 - ***** Evaluation results *****
2025-07-28 13:35:21,647 -   f1 = 0.1952
2025-07-28 13:35:21,647 -   macro_f1 = 0.1952
2025-07-28 13:35:21,647 -   macro_prec = 0.1922
2025-07-28 13:35:21,648 -   macro_rec = 0.265
2025-07-28 13:35:21,648 -   micro_f1 = 0.4937
2025-07-28 13:35:21,648 -   prec = 0.1922
2025-07-28 13:35:21,648 -   rec = 0.265
2025-07-28 13:35:21,649 -   samples_f1 = 0.4925
2025-07-28 13:35:21,649 - ***** Epoch: 1: Eval results *****
2025-07-28 13:35:21,651 -   best_eval_score = 0.0
2025-07-28 13:35:21,651 -   eval_score = 0.1952
2025-07-28 13:35:21,651 -   train_loss = 1.5091
2025-07-28 13:52:26,710 - ***** Evaluation results *****
2025-07-28 13:52:26,711 -   f1 = 0.2425
2025-07-28 13:52:26,711 -   macro_f1 = 0.2425
2025-07-28 13:52:26,711 -   macro_prec = 0.2581
2025-07-28 13:52:26,712 -   macro_rec = 0.3078
2025-07-28 13:52:26,712 -   micro_f1 = 0.5186
2025-07-28 13:52:26,712 -   prec = 0.2581
2025-07-28 13:52:26,712 -   rec = 0.3078
2025-07-28 13:52:26,713 -   samples_f1 = 0.5202
2025-07-28 13:52:26,713 - ***** Epoch: 2: Eval results *****
2025-07-28 13:52:26,713 -   best_eval_score = 0.1952
2025-07-28 13:52:26,714 -   eval_score = 0.2425
2025-07-28 13:52:26,714 -   train_loss = 1.3827
2025-07-28 14:09:27,560 - ***** Evaluation results *****
2025-07-28 14:09:27,560 -   f1 = 0.2462
2025-07-28 14:09:27,561 -   macro_f1 = 0.2462
2025-07-28 14:09:27,561 -   macro_prec = 0.2575
2025-07-28 14:09:27,561 -   macro_rec = 0.287
2025-07-28 14:09:27,561 -   micro_f1 = 0.54
2025-07-28 14:09:27,562 -   prec = 0.2575
2025-07-28 14:09:27,562 -   rec = 0.287
2025-07-28 14:09:27,562 -   samples_f1 = 0.5462
2025-07-28 14:09:27,563 - ***** Epoch: 3: Eval results *****
2025-07-28 14:09:27,563 -   best_eval_score = 0.2425
2025-07-28 14:09:27,563 -   eval_score = 0.2462
2025-07-28 14:09:27,563 -   train_loss = 1.3406
2025-07-28 14:26:13,609 - ***** Evaluation results *****
2025-07-28 14:26:13,609 -   f1 = 0.2835
2025-07-28 14:26:13,610 -   macro_f1 = 0.2835
2025-07-28 14:26:13,610 -   macro_prec = 0.2716
2025-07-28 14:26:13,610 -   macro_rec = 0.3756
2025-07-28 14:26:13,610 -   micro_f1 = 0.5246
2025-07-28 14:26:13,611 -   prec = 0.2716
2025-07-28 14:26:13,611 -   rec = 0.3756
2025-07-28 14:26:13,611 -   samples_f1 = 0.5326
2025-07-28 14:26:13,612 - ***** Epoch: 4: Eval results *****
2025-07-28 14:26:13,612 -   best_eval_score = 0.2462
2025-07-28 14:26:13,612 -   eval_score = 0.2835
2025-07-28 14:26:13,612 -   train_loss = 1.311
2025-07-28 14:40:16,287 - ***** Evaluation results *****
2025-07-28 14:40:16,287 -   f1 = 0.2908
2025-07-28 14:40:16,287 -   macro_f1 = 0.2908
2025-07-28 14:40:16,287 -   macro_prec = 0.3531
2025-07-28 14:40:16,287 -   macro_rec = 0.342
2025-07-28 14:40:16,287 -   micro_f1 = 0.5384
2025-07-28 14:40:16,288 -   prec = 0.3531
2025-07-28 14:40:16,288 -   rec = 0.342
2025-07-28 14:40:16,288 -   samples_f1 = 0.5427
2025-07-28 14:40:16,288 - ***** Epoch: 5: Eval results *****
2025-07-28 14:40:16,288 -   best_eval_score = 0.2835
2025-07-28 14:40:16,288 -   eval_score = 0.2908
2025-07-28 14:40:16,288 -   train_loss = 1.2888
2025-07-28 14:51:50,210 - ***** Evaluation results *****
2025-07-28 14:51:50,211 -   f1 = 0.296
2025-07-28 14:51:50,211 -   macro_f1 = 0.296
2025-07-28 14:51:50,211 -   macro_prec = 0.2945
2025-07-28 14:51:50,211 -   macro_rec = 0.3931
2025-07-28 14:51:50,211 -   micro_f1 = 0.5321
2025-07-28 14:51:50,211 -   prec = 0.2945
2025-07-28 14:51:50,211 -   rec = 0.3931
2025-07-28 14:51:50,211 -   samples_f1 = 0.5373
2025-07-28 14:51:50,212 - ***** Epoch: 6: Eval results *****
2025-07-28 14:51:50,212 -   best_eval_score = 0.2908
2025-07-28 14:51:50,212 -   eval_score = 0.296
2025-07-28 14:51:50,212 -   train_loss = 1.2692
