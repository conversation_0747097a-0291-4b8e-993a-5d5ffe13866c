2025-07-28 12:34:42,627 - ============================== Params ==============================
2025-07-28 12:34:42,627 - logger_name: mult_MINE_goal_2025-07-28-12-34-42
2025-07-28 12:34:42,627 - dataset: MINE
2025-07-28 12:34:42,627 - data_mode: goal
2025-07-28 12:34:42,627 - method: mult
2025-07-28 12:34:42,627 - text_backbone: bert-base-uncased
2025-07-28 12:34:42,627 - seed: 0
2025-07-28 12:34:42,627 - num_workers: 8
2025-07-28 12:34:42,627 - gpu_id: 0
2025-07-28 12:34:42,627 - data_path: ../mine-dataset
2025-07-28 12:34:42,627 - train: True
2025-07-28 12:34:42,628 - tune: False
2025-07-28 12:34:42,628 - save_model: True
2025-07-28 12:34:42,628 - save_results: False
2025-07-28 12:34:42,628 - log_path: logs
2025-07-28 12:34:42,628 - cache_path: cache
2025-07-28 12:34:42,628 - video_data_path: video_data
2025-07-28 12:34:42,628 - audio_data_path: audio_data
2025-07-28 12:34:42,628 - video_feats_path: video_feats.pkl
2025-07-28 12:34:42,628 - audio_feats_path: audio_feats.pkl
2025-07-28 12:34:42,628 - results_path: results
2025-07-28 12:34:42,628 - output_path: outputs
2025-07-28 12:34:42,628 - model_path: models
2025-07-28 12:34:42,628 - config_file_name: mult_bert
2025-07-28 12:34:42,628 - results_file_name: mult_bert.csv
2025-07-28 12:34:42,628 - padding_mode: zero
2025-07-28 12:34:42,628 - padding_loc: end
2025-07-28 12:34:42,628 - need_aligned: False
2025-07-28 12:34:42,628 - eval_monitor: f1
2025-07-28 12:34:42,628 - train_batch_size: 16
2025-07-28 12:34:42,628 - eval_batch_size: 8
2025-07-28 12:34:42,628 - test_batch_size: 8
2025-07-28 12:34:42,628 - wait_patience: 8
2025-07-28 12:34:42,628 - class_num_intent: 21
2025-07-28 12:34:42,628 - class_num_emotion: 11
2025-07-28 12:34:42,628 - num_train_epochs: 100
2025-07-28 12:34:42,628 - dst_feature_dims: 120
2025-07-28 12:34:42,629 - nheads: 8
2025-07-28 12:34:42,629 - n_levels: 8
2025-07-28 12:34:42,629 - attn_dropout: 0.0
2025-07-28 12:34:42,629 - attn_dropout_v: 0.2
2025-07-28 12:34:42,629 - attn_dropout_a: 0.2
2025-07-28 12:34:42,629 - relu_dropout: 0.0
2025-07-28 12:34:42,629 - embed_dropout: 0.1
2025-07-28 12:34:42,629 - res_dropout: 0.0
2025-07-28 12:34:42,629 - output_dropout: 0.2
2025-07-28 12:34:42,629 - text_dropout: 0.4
2025-07-28 12:34:42,629 - grad_clip: 0.5
2025-07-28 12:34:42,629 - attn_mask: True
2025-07-28 12:34:42,629 - conv1d_kernel_size_l: 5
2025-07-28 12:34:42,629 - conv1d_kernel_size_v: 1
2025-07-28 12:34:42,629 - conv1d_kernel_size_a: 1
2025-07-28 12:34:42,629 - lr: 3e-05
2025-07-28 12:34:42,629 - pred_output_path: outputs/mult_MINE_goal_2025-07-28-12-34-42
2025-07-28 12:34:42,629 - model_output_path: outputs/mult_MINE_goal_2025-07-28-12-34-42/models
2025-07-28 12:34:42,629 - ============================== End Params ==============================
2025-07-28 12:34:53,718 - Multimodal intent recognition begins...
2025-07-28 12:34:53,719 - Training begins...
2025-07-28 12:52:02,880 - ***** Evaluation results *****
2025-07-28 12:52:02,881 -   f1 = 0.1952
2025-07-28 12:52:02,881 -   macro_f1 = 0.1952
2025-07-28 12:52:02,881 -   macro_prec = 0.1922
2025-07-28 12:52:02,882 -   macro_rec = 0.265
2025-07-28 12:52:02,882 -   micro_f1 = 0.4937
2025-07-28 12:52:02,882 -   prec = 0.1922
2025-07-28 12:52:02,882 -   rec = 0.265
2025-07-28 12:52:02,882 -   samples_f1 = 0.4925
2025-07-28 12:52:02,883 - ***** Epoch: 1: Eval results *****
2025-07-28 12:52:02,885 -   best_eval_score = 0.0
2025-07-28 12:52:02,885 -   eval_score = 0.1952
2025-07-28 12:52:02,885 -   train_loss = 1.5091
