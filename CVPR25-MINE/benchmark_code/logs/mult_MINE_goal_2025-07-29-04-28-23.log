2025-07-29 04:28:23,843 - ============================== Params ==============================
2025-07-29 04:28:23,843 - logger_name: mult_MINE_goal_2025-07-29-04-28-23
2025-07-29 04:28:23,844 - dataset: MINE
2025-07-29 04:28:23,844 - data_mode: goal
2025-07-29 04:28:23,844 - method: mult
2025-07-29 04:28:23,844 - text_backbone: bert-base-uncased
2025-07-29 04:28:23,844 - seed: 0
2025-07-29 04:28:23,844 - num_workers: 8
2025-07-29 04:28:23,844 - gpu_id: 0
2025-07-29 04:28:23,845 - data_path: ../mine-dataset
2025-07-29 04:28:23,845 - train: True
2025-07-29 04:28:23,845 - tune: False
2025-07-29 04:28:23,845 - save_model: True
2025-07-29 04:28:23,845 - save_results: True
2025-07-29 04:28:23,845 - log_path: logs
2025-07-29 04:28:23,845 - cache_path: cache
2025-07-29 04:28:23,846 - video_data_path: video_data
2025-07-29 04:28:23,846 - audio_data_path: audio_data
2025-07-29 04:28:23,846 - video_feats_path: video_feats.pkl
2025-07-29 04:28:23,846 - audio_feats_path: audio_feats.pkl
2025-07-29 04:28:23,846 - results_path: results
2025-07-29 04:28:23,846 - output_path: outputs
2025-07-29 04:28:23,846 - model_path: models
2025-07-29 04:28:23,846 - config_file_name: mult_bert
2025-07-29 04:28:23,847 - results_file_name: mult_bert.csv
2025-07-29 04:28:23,847 - padding_mode: zero
2025-07-29 04:28:23,847 - padding_loc: end
2025-07-29 04:28:23,847 - need_aligned: False
2025-07-29 04:28:23,847 - eval_monitor: f1
2025-07-29 04:28:23,847 - train_batch_size: 16
2025-07-29 04:28:23,847 - eval_batch_size: 8
2025-07-29 04:28:23,848 - test_batch_size: 8
2025-07-29 04:28:23,848 - wait_patience: 8
2025-07-29 04:28:23,848 - class_num_intent: 21
2025-07-29 04:28:23,848 - class_num_emotion: 11
2025-07-29 04:28:23,848 - num_train_epochs: 100
2025-07-29 04:28:23,848 - dst_feature_dims: 120
2025-07-29 04:28:23,848 - nheads: 8
2025-07-29 04:28:23,849 - n_levels: 8
2025-07-29 04:28:23,849 - attn_dropout: 0.0
2025-07-29 04:28:23,849 - attn_dropout_v: 0.2
2025-07-29 04:28:23,849 - attn_dropout_a: 0.2
2025-07-29 04:28:23,849 - relu_dropout: 0.0
2025-07-29 04:28:23,849 - embed_dropout: 0.1
2025-07-29 04:28:23,849 - res_dropout: 0.0
2025-07-29 04:28:23,849 - output_dropout: 0.2
2025-07-29 04:28:23,849 - text_dropout: 0.4
2025-07-29 04:28:23,850 - grad_clip: 0.5
2025-07-29 04:28:23,850 - attn_mask: True
2025-07-29 04:28:23,850 - conv1d_kernel_size_l: 5
2025-07-29 04:28:23,850 - conv1d_kernel_size_v: 1
2025-07-29 04:28:23,850 - conv1d_kernel_size_a: 1
2025-07-29 04:28:23,850 - lr: 3e-05
2025-07-29 04:28:23,850 - pred_output_path: outputs/mult_MINE_goal_2025-07-29-04-28-23
2025-07-29 04:28:23,851 - model_output_path: outputs/mult_MINE_goal_2025-07-29-04-28-23/models
2025-07-29 04:28:23,851 - ============================== End Params ==============================
2025-07-29 04:28:37,207 - Multimodal intent recognition begins...
2025-07-29 04:28:37,207 - Training begins...
2025-07-29 04:44:39,251 - ***** Evaluation results *****
2025-07-29 04:44:39,251 -   f1 = 0.1952
2025-07-29 04:44:39,251 -   macro_f1 = 0.1952
2025-07-29 04:44:39,252 -   macro_prec = 0.1922
2025-07-29 04:44:39,252 -   macro_rec = 0.265
2025-07-29 04:44:39,252 -   micro_f1 = 0.4937
2025-07-29 04:44:39,252 -   prec = 0.1922
2025-07-29 04:44:39,253 -   rec = 0.265
2025-07-29 04:44:39,253 -   samples_f1 = 0.4925
2025-07-29 04:44:39,253 - ***** Epoch: 1: Eval results *****
2025-07-29 04:44:39,255 -   best_eval_score = 0.0
2025-07-29 04:44:39,255 -   eval_score = 0.1952
2025-07-29 04:44:39,255 -   train_loss = 1.5091
2025-07-29 05:00:47,057 - ***** Evaluation results *****
2025-07-29 05:00:47,057 -   f1 = 0.2425
2025-07-29 05:00:47,058 -   macro_f1 = 0.2425
2025-07-29 05:00:47,058 -   macro_prec = 0.2581
2025-07-29 05:00:47,058 -   macro_rec = 0.3078
2025-07-29 05:00:47,058 -   micro_f1 = 0.5186
2025-07-29 05:00:47,059 -   prec = 0.2581
2025-07-29 05:00:47,059 -   rec = 0.3078
2025-07-29 05:00:47,059 -   samples_f1 = 0.5202
2025-07-29 05:00:47,060 - ***** Epoch: 2: Eval results *****
2025-07-29 05:00:47,060 -   best_eval_score = 0.1952
2025-07-29 05:00:47,060 -   eval_score = 0.2425
2025-07-29 05:00:47,060 -   train_loss = 1.3827
2025-07-29 05:17:06,996 - ***** Evaluation results *****
2025-07-29 05:17:06,997 -   f1 = 0.2462
2025-07-29 05:17:06,997 -   macro_f1 = 0.2462
2025-07-29 05:17:06,997 -   macro_prec = 0.2575
2025-07-29 05:17:06,997 -   macro_rec = 0.287
2025-07-29 05:17:06,997 -   micro_f1 = 0.54
2025-07-29 05:17:06,997 -   prec = 0.2575
2025-07-29 05:17:06,998 -   rec = 0.287
2025-07-29 05:17:06,998 -   samples_f1 = 0.5462
2025-07-29 05:17:06,999 - ***** Epoch: 3: Eval results *****
2025-07-29 05:17:06,999 -   best_eval_score = 0.2425
2025-07-29 05:17:06,999 -   eval_score = 0.2462
2025-07-29 05:17:07,000 -   train_loss = 1.3406
2025-07-29 05:33:05,762 - ***** Evaluation results *****
2025-07-29 05:33:05,763 -   f1 = 0.2835
2025-07-29 05:33:05,763 -   macro_f1 = 0.2835
2025-07-29 05:33:05,763 -   macro_prec = 0.2716
2025-07-29 05:33:05,764 -   macro_rec = 0.3756
2025-07-29 05:33:05,764 -   micro_f1 = 0.5246
2025-07-29 05:33:05,764 -   prec = 0.2716
2025-07-29 05:33:05,764 -   rec = 0.3756
2025-07-29 05:33:05,765 -   samples_f1 = 0.5326
2025-07-29 05:33:05,765 - ***** Epoch: 4: Eval results *****
2025-07-29 05:33:05,766 -   best_eval_score = 0.2462
2025-07-29 05:33:05,766 -   eval_score = 0.2835
2025-07-29 05:33:05,766 -   train_loss = 1.311
2025-07-29 05:49:24,168 - ***** Evaluation results *****
2025-07-29 05:49:24,168 -   f1 = 0.2908
2025-07-29 05:49:24,168 -   macro_f1 = 0.2908
2025-07-29 05:49:24,168 -   macro_prec = 0.3531
2025-07-29 05:49:24,168 -   macro_rec = 0.342
2025-07-29 05:49:24,169 -   micro_f1 = 0.5384
2025-07-29 05:49:24,169 -   prec = 0.3531
2025-07-29 05:49:24,169 -   rec = 0.342
2025-07-29 05:49:24,169 -   samples_f1 = 0.5427
2025-07-29 05:49:24,169 - ***** Epoch: 5: Eval results *****
2025-07-29 05:49:24,169 -   best_eval_score = 0.2835
2025-07-29 05:49:24,169 -   eval_score = 0.2908
2025-07-29 05:49:24,169 -   train_loss = 1.2888
2025-07-29 06:04:35,562 - ***** Evaluation results *****
2025-07-29 06:04:35,563 -   f1 = 0.296
2025-07-29 06:04:35,563 -   macro_f1 = 0.296
2025-07-29 06:04:35,563 -   macro_prec = 0.2945
2025-07-29 06:04:35,563 -   macro_rec = 0.3931
2025-07-29 06:04:35,564 -   micro_f1 = 0.5321
2025-07-29 06:04:35,564 -   prec = 0.2945
2025-07-29 06:04:35,564 -   rec = 0.3931
2025-07-29 06:04:35,564 -   samples_f1 = 0.5373
2025-07-29 06:04:35,565 - ***** Epoch: 6: Eval results *****
2025-07-29 06:04:35,565 -   best_eval_score = 0.2908
2025-07-29 06:04:35,565 -   eval_score = 0.296
2025-07-29 06:04:35,566 -   train_loss = 1.2692
2025-07-29 06:20:55,788 - ***** Evaluation results *****
2025-07-29 06:20:55,789 -   f1 = 0.2939
2025-07-29 06:20:55,789 -   macro_f1 = 0.2939
2025-07-29 06:20:55,790 -   macro_prec = 0.3153
2025-07-29 06:20:55,790 -   macro_rec = 0.3971
2025-07-29 06:20:55,790 -   micro_f1 = 0.5235
2025-07-29 06:20:55,791 -   prec = 0.3153
2025-07-29 06:20:55,791 -   rec = 0.3971
2025-07-29 06:20:55,791 -   samples_f1 = 0.5368
2025-07-29 06:20:55,792 - ***** Epoch: 7: Eval results *****
2025-07-29 06:20:55,792 -   best_eval_score = 0.296
2025-07-29 06:20:55,792 -   eval_score = 0.2939
2025-07-29 06:20:55,793 -   train_loss = 1.2554
2025-07-29 06:20:55,793 - EarlyStopping counter: 1 out of 8
2025-07-29 06:37:09,718 - ***** Evaluation results *****
2025-07-29 06:37:09,718 -   f1 = 0.3011
2025-07-29 06:37:09,719 -   macro_f1 = 0.3011
2025-07-29 06:37:09,719 -   macro_prec = 0.3581
2025-07-29 06:37:09,719 -   macro_rec = 0.3682
2025-07-29 06:37:09,720 -   micro_f1 = 0.5202
2025-07-29 06:37:09,720 -   prec = 0.3581
2025-07-29 06:37:09,720 -   rec = 0.3682
2025-07-29 06:37:09,721 -   samples_f1 = 0.5277
2025-07-29 06:37:09,721 - ***** Epoch: 8: Eval results *****
2025-07-29 06:37:09,721 -   best_eval_score = 0.296
2025-07-29 06:37:09,722 -   eval_score = 0.3011
2025-07-29 06:37:09,722 -   train_loss = 1.2384
2025-07-29 06:53:31,599 - ***** Evaluation results *****
2025-07-29 06:53:31,600 -   f1 = 0.3136
2025-07-29 06:53:31,600 -   macro_f1 = 0.3136
2025-07-29 06:53:31,600 -   macro_prec = 0.3099
2025-07-29 06:53:31,600 -   macro_rec = 0.3925
2025-07-29 06:53:31,600 -   micro_f1 = 0.5395
2025-07-29 06:53:31,600 -   prec = 0.3099
2025-07-29 06:53:31,600 -   rec = 0.3925
2025-07-29 06:53:31,600 -   samples_f1 = 0.544
2025-07-29 06:53:31,601 - ***** Epoch: 9: Eval results *****
2025-07-29 06:53:31,601 -   best_eval_score = 0.3011
2025-07-29 06:53:31,601 -   eval_score = 0.3136
2025-07-29 06:53:31,601 -   train_loss = 1.2271
