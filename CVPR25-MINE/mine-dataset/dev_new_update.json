[{"id": "15930", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "16250", "type": [1, 0, 0, 0], "emotion": 2, "goal": [15, 6, 2, 3]}, {"id": "5966", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "897", "type": [1, 1, 0, 0], "emotion": 2, "goal": [18, 6, 4, 3]}, {"id": "10325", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "11870", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 3, 20]}, {"id": "563", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "12662", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 10]}, {"id": "12199", "type": [1, 1, 0, 0], "emotion": 0, "goal": [1, 2]}, {"id": "4231", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 9]}, {"id": "4394", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 3]}, {"id": "5887", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "4024", "type": [1, 1, 0, 0], "emotion": 0, "goal": [13, 1]}, {"id": "13502", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 3]}, {"id": "7122", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "16573", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "6758", "type": [1, 1, 0, 0], "emotion": 0, "goal": [5, 0, 3]}, {"id": "5602", "type": [1, 0, 1, 0], "emotion": 4, "goal": [18, 13]}, {"id": "3396", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 9]}, {"id": "3210", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1626", "type": [1, 0, 1, 1], "emotion": 4, "goal": [3, 11]}, {"id": "17778", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "16353", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12792", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9070", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "1164", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "343", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 11]}, {"id": "338", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "20002", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "325", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "2335", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 15]}, {"id": "5246", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "695", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 5, 15]}, {"id": "3694", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 19]}, {"id": "15959", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "11540", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14281", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "17398", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "19305", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13]}, {"id": "10812", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 6, 15, 3]}, {"id": "1606", "type": [1, 1, 0, 0], "emotion": 4, "goal": [16]}, {"id": "1301", "type": [1, 0, 1, 1], "emotion": 5, "goal": [5, 6]}, {"id": "497", "type": [1, 1, 1, 1], "emotion": 4, "goal": [4, 3]}, {"id": "4694", "type": [1, 1, 0, 0], "emotion": 6, "goal": [15, 16]}, {"id": "13591", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 6]}, {"id": "15753", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "7083", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "2612", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15, 2, 3, 6]}, {"id": "18125", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "1883", "type": [1, 1, 0, 0], "emotion": 7, "goal": [7, 3]}, {"id": "4755", "type": [1, 0, 0, 0], "emotion": 8, "goal": [3, 0]}, {"id": "483", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6]}, {"id": "6388", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15, 10]}, {"id": "15812", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 12]}, {"id": "6484", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "15221", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 3]}, {"id": "5983", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "19941", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "2471", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15, 10]}, {"id": "7961", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 5, 15]}, {"id": "10838", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 0]}, {"id": "8891", "type": [1, 0, 1, 0], "emotion": 4, "goal": [11, 16]}, {"id": "4729", "type": [1, 1, 0, 0], "emotion": 5, "goal": [20, 3, 10]}, {"id": "19755", "type": [1, 1, 0, 0], "emotion": 0, "goal": [3, 11, 0]}, {"id": "19052", "type": [1, 0, 0, 0], "emotion": 8, "goal": [11, 3, 0]}, {"id": "4678", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "4907", "type": [1, 1, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "16926", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "10909", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 3]}, {"id": "10026", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17862", "type": [1, 0, 0, 0], "emotion": 5, "goal": [3, 20]}, {"id": "16005", "type": [1, 0, 0, 0], "emotion": 0, "goal": [3, 0, 1]}, {"id": "18145", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "8246", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 16]}, {"id": "12672", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 6, 3]}, {"id": "6331", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "14405", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 10]}, {"id": "1599", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "4193", "type": [1, 1, 0, 0], "emotion": 6, "goal": [3, 17, 15, 4]}, {"id": "8044", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 5]}, {"id": "545", "type": [1, 1, 0, 0], "emotion": 6, "goal": [2, 9]}, {"id": "15757", "type": [1, 1, 0, 0], "emotion": 0, "goal": [3, 12, 0]}, {"id": "11774", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3645", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "18645", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 13]}, {"id": "2973", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 11, 1]}, {"id": "11091", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "861", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "20039", "type": [1, 0, 0, 0], "emotion": 2, "goal": [20, 3, 15, 13]}, {"id": "4145", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "15681", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19082", "type": [1, 0, 1, 0], "emotion": 4, "goal": [18, 3]}, {"id": "14809", "type": [1, 0, 0, 0], "emotion": 10, "goal": [13, 3]}, {"id": "7512", "type": [1, 1, 0, 0], "emotion": 6, "goal": [13, 20]}, {"id": "14388", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "10102", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4222", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "17210", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 6]}, {"id": "2986", "type": [1, 0, 1, 0], "emotion": 2, "goal": [18, 3]}, {"id": "11312", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "15909", "type": [1, 0, 1, 1], "emotion": 4, "goal": [18, 13, 3]}, {"id": "13845", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "18230", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "10871", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "5431", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "9108", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "6824", "type": [1, 1, 0, 0], "emotion": 3, "goal": [11, 16]}, {"id": "13028", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 19]}, {"id": "2332", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 1]}, {"id": "18261", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "1900", "type": [1, 1, 0, 0], "emotion": 7, "goal": [19, 4]}, {"id": "12032", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 16]}, {"id": "5327", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "20141", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15, 10]}, {"id": "6944", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 16]}, {"id": "2338", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "17479", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "919", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "18046", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "2898", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "9531", "type": [1, 0, 1, 0], "emotion": 4, "goal": [6, 3]}, {"id": "13566", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "11078", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 10]}, {"id": "6909", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "3695", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "19172", "type": [1, 0, 0, 0], "emotion": 7, "goal": [7, 12]}, {"id": "14538", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "20013", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19859", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4]}, {"id": "10618", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "8663", "type": [1, 1, 0, 0], "emotion": 7, "goal": [3, 16]}, {"id": "13959", "type": [1, 1, 0, 0], "emotion": 5, "goal": [6, 15, 3]}, {"id": "15925", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15]}, {"id": "19977", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16819", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "1968", "type": [1, 1, 0, 0], "emotion": 6, "goal": [13, 17]}, {"id": "8780", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 1, 0]}, {"id": "2253", "type": [1, 0, 1, 0], "emotion": 4, "goal": [16]}, {"id": "2095", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 15]}, {"id": "2254", "type": [1, 1, 1, 0], "emotion": 4, "goal": [13, 4]}, {"id": "5151", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "10323", "type": [1, 0, 0, 0], "emotion": 2, "goal": [7, 20, 3]}, {"id": "13117", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "16635", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "255", "type": [1, 1, 0, 0], "emotion": 1, "goal": [7, 2]}, {"id": "2712", "type": [1, 0, 0, 0], "emotion": 6, "goal": [17, 4]}, {"id": "14352", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13]}, {"id": "9704", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 13]}, {"id": "18886", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "11997", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15, 10]}, {"id": "19536", "type": [1, 1, 0, 0], "emotion": 5, "goal": [17, 13, 15, 3]}, {"id": "16362", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1920", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6201", "type": [1, 0, 0, 0], "emotion": 0, "goal": [3, 11, 0]}, {"id": "15751", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 12]}, {"id": "18542", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 0]}, {"id": "7084", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "19097", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "7917", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "300", "type": [1, 0, 0, 0], "emotion": 4, "goal": [8, 3]}, {"id": "17007", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 15]}, {"id": "13741", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4721", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6522", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 3, 6]}, {"id": "3291", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13, 3]}, {"id": "3047", "type": [1, 0, 1, 0], "emotion": 4, "goal": [6, 15, 10]}, {"id": "972", "type": [1, 1, 0, 0], "emotion": 7, "goal": [4, 15, 3]}, {"id": "9635", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 16]}, {"id": "17227", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "2695", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3]}, {"id": "15520", "type": [1, 0, 0, 0], "emotion": 1, "goal": [3, 16]}, {"id": "1571", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "19098", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "5477", "type": [1, 0, 1, 1], "emotion": 2, "goal": [10, 3, 6]}, {"id": "11959", "type": [1, 1, 0, 0], "emotion": 7, "goal": [13, 3]}, {"id": "4910", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 3]}, {"id": "1021", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 15, 4]}, {"id": "6506", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "7198", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "17073", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1050", "type": [1, 0, 1, 1], "emotion": 1, "goal": [3, 20]}, {"id": "9271", "type": [1, 1, 0, 0], "emotion": 5, "goal": [15, 20, 3]}, {"id": "5303", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "8033", "type": [1, 1, 0, 0], "emotion": 5, "goal": [7, 15, 10, 20]}, {"id": "8085", "type": [1, 0, 0, 0], "emotion": 7, "goal": [7, 3]}, {"id": "3007", "type": [1, 1, 0, 0], "emotion": 7, "goal": [7, 13, 19, 3]}, {"id": "12800", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "8253", "type": [1, 0, 1, 0], "emotion": 4, "goal": [1, 3]}, {"id": "11170", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 11]}, {"id": "13530", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 14]}, {"id": "2032", "type": [1, 1, 0, 0], "emotion": 7, "goal": [3, 16]}, {"id": "8871", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 3]}, {"id": "14130", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "6636", "type": [1, 0, 0, 0], "emotion": 1, "goal": [13, 14, 9, 3]}, {"id": "19617", "type": [1, 1, 0, 0], "emotion": 5, "goal": [17, 3, 19]}, {"id": "532", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13, 5, 3]}, {"id": "1844", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "19093", "type": [1, 1, 0, 0], "emotion": 2, "goal": [10, 16]}, {"id": "8430", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 10]}, {"id": "11612", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15, 3]}, {"id": "7909", "type": [1, 0, 1, 1], "emotion": 2, "goal": [4, 5, 6, 15]}, {"id": "14669", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12000", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "18593", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13880", "type": [1, 0, 0, 0], "emotion": 1, "goal": [3, 9]}, {"id": "6923", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "11070", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "19406", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "1160", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 5, 15]}, {"id": "18030", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5436", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "14535", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 12]}, {"id": "16668", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "16170", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "7714", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15, 12]}, {"id": "3390", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 6, 15]}, {"id": "6557", "type": [1, 0, 0, 0], "emotion": 0, "goal": [17, 3]}, {"id": "13550", "type": [1, 0, 0, 0], "emotion": 0, "goal": [11, 3]}, {"id": "19356", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "9880", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "7214", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15]}, {"id": "2008", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13, 15]}, {"id": "17906", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19621", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "11976", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "14797", "type": [1, 0, 0, 0], "emotion": 4, "goal": [11, 3]}, {"id": "14050", "type": [1, 0, 1, 1], "emotion": 4, "goal": [15, 5, 13, 3]}, {"id": "5194", "type": [1, 0, 0, 0], "emotion": 1, "goal": [12, 3, 0]}, {"id": "1782", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 10]}, {"id": "7654", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "10566", "type": [1, 1, 0, 0], "emotion": 2, "goal": [18, 3]}, {"id": "12938", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 3]}, {"id": "5290", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14765", "type": [1, 1, 0, 0], "emotion": 5, "goal": [17, 13, 3]}, {"id": "2488", "type": [1, 0, 1, 0], "emotion": 2, "goal": [5, 6, 15]}, {"id": "19270", "type": [1, 0, 0, 0], "emotion": 4, "goal": [8, 13, 9, 3]}, {"id": "7358", "type": [1, 0, 0, 0], "emotion": 2, "goal": [20, 11, 3]}, {"id": "52", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 1]}, {"id": "8657", "type": [1, 1, 0, 0], "emotion": 4, "goal": [14, 3]}, {"id": "13359", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "2100", "type": [1, 0, 1, 1], "emotion": 2, "goal": [4, 5, 6, 15]}, {"id": "15091", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "17865", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13, 15, 14, 9, 3]}, {"id": "11559", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14298", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "8107", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 15]}, {"id": "13496", "type": [1, 0, 1, 0], "emotion": 4, "goal": [20, 3]}, {"id": "3619", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "6656", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 0, 11]}, {"id": "1093", "type": [1, 1, 0, 0], "emotion": 1, "goal": [12, 15]}, {"id": "16790", "type": [1, 1, 0, 0], "emotion": 5, "goal": [18, 3]}, {"id": "19360", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 3]}, {"id": "2965", "type": [1, 0, 0, 0], "emotion": 0, "goal": [11, 16]}, {"id": "4568", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "10371", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "1911", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "1510", "type": [1, 0, 0, 0], "emotion": 4, "goal": [0, 3]}, {"id": "4662", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "16886", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 16]}, {"id": "15859", "type": [1, 1, 0, 0], "emotion": 7, "goal": [7, 13, 3]}, {"id": "13059", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "10672", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 11]}, {"id": "14553", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "2024", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 13]}, {"id": "1886", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "9910", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13, 3]}, {"id": "17063", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15, 12]}, {"id": "8708", "type": [1, 0, 0, 0], "emotion": 4, "goal": [16, 13]}, {"id": "168", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 15]}, {"id": "2873", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5092", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "317", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 3, 4]}, {"id": "4584", "type": [1, 0, 0, 0], "emotion": 6, "goal": [7, 3, 10]}, {"id": "3445", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 18]}, {"id": "10022", "type": [1, 0, 0, 0], "emotion": 8, "goal": [13, 3]}, {"id": "6428", "type": [1, 0, 0, 0], "emotion": 4, "goal": [9, 16]}, {"id": "18950", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "10967", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 10]}, {"id": "10702", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 13]}, {"id": "18210", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "8421", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 3, 20]}, {"id": "6996", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "14590", "type": [1, 1, 0, 0], "emotion": 2, "goal": [8, 6]}, {"id": "3278", "type": [1, 0, 0, 0], "emotion": 3, "goal": [13, 0, 3, 11, 1]}, {"id": "18705", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13]}, {"id": "12530", "type": [1, 1, 0, 0], "emotion": 4, "goal": [20, 3, 13, 15, 12]}, {"id": "2076", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 3]}, {"id": "10907", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4]}, {"id": "1855", "type": [1, 0, 0, 0], "emotion": 8, "goal": [8, 0]}, {"id": "12386", "type": [1, 0, 1, 0], "emotion": 2, "goal": [15, 16]}, {"id": "1030", "type": [1, 0, 1, 1], "emotion": 8, "goal": [12, 13]}, {"id": "7603", "type": [1, 0, 1, 1], "emotion": 4, "goal": [3, 16]}, {"id": "3297", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13]}, {"id": "5595", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "10277", "type": [1, 0, 0, 0], "emotion": 0, "goal": [3, 16]}, {"id": "6906", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 16]}, {"id": "19958", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "17182", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3, 20]}, {"id": "7140", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 16]}, {"id": "14493", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "1991", "type": [1, 1, 0, 0], "emotion": 7, "goal": [3, 16]}, {"id": "10396", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "18337", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 13]}, {"id": "15863", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 13]}, {"id": "8332", "type": [1, 0, 1, 0], "emotion": 4, "goal": [18, 12]}, {"id": "2475", "type": [1, 0, 0, 0], "emotion": 9, "goal": [13, 3]}, {"id": "4418", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 4, 12]}, {"id": "19454", "type": [1, 1, 0, 0], "emotion": 0, "goal": [3, 11]}, {"id": "5493", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 15]}, {"id": "1549", "type": [1, 0, 1, 1], "emotion": 6, "goal": [18, 4, 13]}, {"id": "14774", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "17779", "type": [1, 0, 1, 1], "emotion": 2, "goal": [4, 5, 15, 3]}, {"id": "5376", "type": [1, 0, 1, 1], "emotion": 4, "goal": [12, 13]}, {"id": "11557", "type": [1, 0, 0, 0], "emotion": 4, "goal": [19, 14, 3]}, {"id": "912", "type": [1, 0, 1, 1], "emotion": 5, "goal": [18, 13]}, {"id": "12709", "type": [1, 1, 0, 0], "emotion": 0, "goal": [3, 0]}, {"id": "3930", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4, 12]}, {"id": "18980", "type": [1, 1, 0, 0], "emotion": 5, "goal": [18, 3]}, {"id": "3717", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 6, 4]}, {"id": "17690", "type": [1, 0, 0, 0], "emotion": 9, "goal": [18, 12]}, {"id": "10448", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 16]}, {"id": "8761", "type": [1, 0, 0, 0], "emotion": 8, "goal": [3, 11]}, {"id": "7585", "type": [1, 0, 0, 0], "emotion": 4, "goal": [11, 20, 3]}, {"id": "14960", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "7581", "type": [1, 0, 0, 0], "emotion": 7, "goal": [7, 3]}, {"id": "4364", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "17234", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17446", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "12391", "type": [1, 0, 1, 1], "emotion": 1, "goal": [13, 3]}, {"id": "1732", "type": [1, 1, 0, 0], "emotion": 2, "goal": [7, 11]}, {"id": "6375", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "4598", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 16]}, {"id": "11762", "type": [1, 1, 0, 0], "emotion": 7, "goal": [15, 13, 3, 4]}, {"id": "20079", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 20]}, {"id": "14772", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5852", "type": [1, 1, 0, 0], "emotion": 4, "goal": [19, 12]}, {"id": "451", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 4]}, {"id": "7981", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4, 3]}, {"id": "6337", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9043", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19181", "type": [1, 0, 1, 1], "emotion": 4, "goal": [11, 16]}, {"id": "13515", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15, 20, 3]}, {"id": "9115", "type": [1, 1, 0, 0], "emotion": 1, "goal": [14, 9, 3]}, {"id": "2907", "type": [1, 0, 1, 1], "emotion": 2, "goal": [4, 5, 17]}, {"id": "9265", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "14890", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 15, 3, 13]}, {"id": "842", "type": [1, 0, 1, 0], "emotion": 2, "goal": [4, 6]}, {"id": "16677", "type": [1, 1, 0, 0], "emotion": 8, "goal": [5, 1, 0]}, {"id": "6529", "type": [1, 1, 0, 0], "emotion": 7, "goal": [3, 2]}, {"id": "7075", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "17397", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "8207", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "1211", "type": [1, 0, 0, 0], "emotion": 7, "goal": [16]}, {"id": "3835", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "19222", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17248", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5236", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5564", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 0]}, {"id": "20031", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 1]}, {"id": "4105", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "9463", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 13, 4]}, {"id": "12548", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 13, 3]}, {"id": "12390", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "8524", "type": [1, 1, 0, 0], "emotion": 6, "goal": [15, 17]}, {"id": "12732", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 4]}, {"id": "3611", "type": [1, 0, 1, 0], "emotion": 4, "goal": [16]}, {"id": "5278", "type": [1, 1, 0, 0], "emotion": 3, "goal": [16]}, {"id": "14518", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 16]}, {"id": "8126", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 18, 4]}, {"id": "3788", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "10326", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "7562", "type": [1, 0, 1, 1], "emotion": 7, "goal": [12, 11]}, {"id": "10580", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "13977", "type": [1, 1, 0, 0], "emotion": 5, "goal": [4, 15, 3, 20]}, {"id": "17379", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 10]}, {"id": "6690", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15, 5]}, {"id": "6083", "type": [1, 0, 0, 0], "emotion": 1, "goal": [3, 16]}, {"id": "7828", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13, 3]}, {"id": "5994", "type": [1, 0, 1, 0], "emotion": 0, "goal": [0, 11]}, {"id": "1543", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 15]}, {"id": "12261", "type": [1, 0, 1, 1], "emotion": 5, "goal": [13, 16]}, {"id": "11082", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 18]}, {"id": "14650", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 3]}, {"id": "15462", "type": [1, 1, 0, 0], "emotion": 8, "goal": [5, 3, 11, 0]}, {"id": "13913", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 3]}, {"id": "7233", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13927", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "15942", "type": [1, 0, 1, 1], "emotion": 0, "goal": [11, 0]}, {"id": "14594", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13, 3]}, {"id": "19561", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4471", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "13206", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 16]}, {"id": "7576", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "9979", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "18073", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 15]}, {"id": "8933", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "18359", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "13061", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 3]}, {"id": "222", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 19, 20]}, {"id": "13579", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14429", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "3107", "type": [1, 0, 0, 0], "emotion": 8, "goal": [0, 3]}, {"id": "16123", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 11]}, {"id": "8895", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "8645", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 6, 15, 3]}, {"id": "18001", "type": [1, 0, 0, 0], "emotion": 0, "goal": [9, 3, 0, 1]}, {"id": "5797", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "8123", "type": [1, 1, 0, 0], "emotion": 2, "goal": [7, 6]}, {"id": "18617", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "5593", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 11]}, {"id": "3356", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 4, 5, 15, 3]}, {"id": "1746", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 0, 16]}, {"id": "5612", "type": [1, 0, 1, 0], "emotion": 4, "goal": [11, 16]}, {"id": "16963", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "7904", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15, 6, 3]}, {"id": "9583", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 13, 4]}, {"id": "8220", "type": [1, 0, 0, 0], "emotion": 4, "goal": [0, 16]}, {"id": "5160", "type": [1, 0, 1, 0], "emotion": 4, "goal": [6, 15, 13, 3]}, {"id": "7335", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 9]}, {"id": "11232", "type": [1, 1, 0, 0], "emotion": 3, "goal": [13, 16]}, {"id": "15212", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 15]}, {"id": "10276", "type": [1, 0, 1, 1], "emotion": 4, "goal": [12, 16]}, {"id": "6146", "type": [1, 0, 0, 0], "emotion": 8, "goal": [0, 11]}, {"id": "19615", "type": [1, 0, 1, 1], "emotion": 2, "goal": [15, 5, 3]}, {"id": "14643", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "15576", "type": [1, 0, 0, 0], "emotion": 8, "goal": [0, 1]}, {"id": "3121", "type": [1, 0, 1, 1], "emotion": 1, "goal": [13, 15, 3, 9]}, {"id": "16714", "type": [1, 0, 1, 1], "emotion": 4, "goal": [3, 16]}, {"id": "18703", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3, 6]}, {"id": "6281", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "725", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 11]}, {"id": "1597", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "13529", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "13751", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "8664", "type": [1, 0, 0, 0], "emotion": 0, "goal": [3, 1, 0]}, {"id": "898", "type": [1, 0, 1, 0], "emotion": 3, "goal": [11, 18]}, {"id": "11417", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 3]}, {"id": "3998", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "11050", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "10576", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "8839", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "17032", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12322", "type": [1, 1, 0, 0], "emotion": 3, "goal": [0, 1, 3]}, {"id": "19829", "type": [1, 1, 0, 0], "emotion": 4, "goal": [8, 3]}, {"id": "3706", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "12983", "type": [1, 0, 1, 0], "emotion": 2, "goal": [18, 6, 3]}, {"id": "14719", "type": [1, 1, 0, 0], "emotion": 1, "goal": [4, 13, 3]}, {"id": "18279", "type": [1, 0, 0, 0], "emotion": 4, "goal": [16, 3]}, {"id": "703", "type": [1, 0, 0, 0], "emotion": 7, "goal": [12]}, {"id": "11805", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15, 6]}, {"id": "6627", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "4549", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "11334", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13783", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 12, 3]}, {"id": "16658", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "1010", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4644", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "6951", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "8197", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "1519", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 4, 10]}, {"id": "8306", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "11713", "type": [1, 1, 0, 0], "emotion": 7, "goal": [12, 3]}, {"id": "6106", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "15705", "type": [1, 1, 0, 0], "emotion": 0, "goal": [13, 3, 11]}, {"id": "4955", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14924", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 14, 9, 3]}, {"id": "17458", "type": [1, 0, 1, 0], "emotion": 4, "goal": [12, 3]}, {"id": "9634", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "17454", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14006", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5357", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 6, 15]}, {"id": "2538", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "7506", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "14976", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "14928", "type": [1, 1, 0, 0], "emotion": 5, "goal": [18, 13, 4, 6, 3]}, {"id": "7450", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "16916", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 15]}, {"id": "3507", "type": [1, 1, 0, 0], "emotion": 5, "goal": [5, 6, 15, 10]}, {"id": "12106", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 10]}, {"id": "8274", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "8122", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6931", "type": [1, 1, 0, 0], "emotion": 8, "goal": [0, 1]}, {"id": "18489", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "12754", "type": [1, 0, 0, 0], "emotion": 0, "goal": [6, 0, 8]}, {"id": "417", "type": [1, 0, 0, 0], "emotion": 0, "goal": [11, 13]}, {"id": "6402", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "11441", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "11743", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15, 3]}, {"id": "2643", "type": [1, 0, 0, 0], "emotion": 4, "goal": [7]}, {"id": "734", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13]}, {"id": "10902", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "15558", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "17247", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "6566", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5399", "type": [1, 1, 0, 0], "emotion": 7, "goal": [13, 3]}, {"id": "1778", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "10311", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17768", "type": [1, 0, 1, 1], "emotion": 4, "goal": [10, 3, 20]}, {"id": "16490", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "18289", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "15583", "type": [1, 0, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "894", "type": [1, 1, 0, 0], "emotion": 0, "goal": [11, 1, 0]}, {"id": "9015", "type": [1, 0, 1, 0], "emotion": 5, "goal": [6, 15, 10]}, {"id": "4656", "type": [1, 0, 0, 0], "emotion": 1, "goal": [16, 13]}, {"id": "9720", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "9343", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "15296", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "16912", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 8, 3]}, {"id": "17349", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "8731", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "8088", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17101", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 13]}, {"id": "3567", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 5, 13, 15, 10]}, {"id": "4212", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 5]}, {"id": "7245", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17035", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "15166", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 16]}, {"id": "17105", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "5711", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "2588", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 14, 3]}, {"id": "3630", "type": [1, 0, 0, 0], "emotion": 8, "goal": [11, 16]}, {"id": "4350", "type": [1, 1, 0, 0], "emotion": 4, "goal": [7, 16]}, {"id": "14002", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 9, 13, 15]}, {"id": "19558", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 6, 15, 3]}, {"id": "5107", "type": [1, 0, 1, 0], "emotion": 4, "goal": [0, 11, 3, 13]}, {"id": "19385", "type": [1, 1, 0, 0], "emotion": 7, "goal": [6, 3]}, {"id": "5188", "type": [1, 1, 0, 0], "emotion": 1, "goal": [17, 4, 5]}, {"id": "10352", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "19430", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6445", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 4]}, {"id": "10678", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 18]}, {"id": "9524", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 11]}, {"id": "19347", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 6, 15]}, {"id": "7606", "type": [1, 0, 1, 0], "emotion": 4, "goal": [4, 3]}, {"id": "11834", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 15]}, {"id": "14308", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "3199", "type": [1, 0, 1, 0], "emotion": 2, "goal": [8, 3, 14, 2]}, {"id": "20165", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 4, 3]}, {"id": "17733", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 3]}, {"id": "3808", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "17376", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9250", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15, 10]}, {"id": "17501", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "15625", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 15]}, {"id": "10501", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 11]}, {"id": "2872", "type": [1, 0, 1, 0], "emotion": 5, "goal": [12, 16]}, {"id": "7382", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "10697", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "12464", "type": [1, 0, 1, 1], "emotion": 2, "goal": [8, 9, 15]}, {"id": "18547", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4252", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 10]}, {"id": "4823", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 15]}, {"id": "16042", "type": [1, 1, 0, 0], "emotion": 0, "goal": [11, 16]}, {"id": "11562", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 11]}, {"id": "14320", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 3]}, {"id": "4460", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "11821", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 15, 6, 12, 20]}, {"id": "19143", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1130", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3]}, {"id": "12148", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "15307", "type": [1, 0, 0, 0], "emotion": 0, "goal": [3, 11]}, {"id": "406", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 5, 4]}, {"id": "4989", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 15, 3]}, {"id": "9535", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 13, 4]}, {"id": "12458", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12162", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 16]}, {"id": "18200", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15, 3]}, {"id": "20155", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 3, 19]}, {"id": "10108", "type": [1, 0, 1, 0], "emotion": 4, "goal": [18, 13, 3]}, {"id": "13646", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "11558", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "947", "type": [1, 0, 0, 0], "emotion": 6, "goal": [17, 4, 5]}, {"id": "14371", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 12]}, {"id": "13639", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 4]}, {"id": "18820", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 15]}, {"id": "8381", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "1004", "type": [1, 1, 0, 0], "emotion": 7, "goal": [15, 13, 3]}, {"id": "13252", "type": [1, 1, 0, 0], "emotion": 8, "goal": [3, 11, 0]}, {"id": "19000", "type": [1, 1, 0, 0], "emotion": 1, "goal": [17, 6]}, {"id": "2642", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 16]}, {"id": "4462", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 10]}, {"id": "7186", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 12]}, {"id": "536", "type": [1, 1, 0, 0], "emotion": 7, "goal": [12]}, {"id": "8872", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5328", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13, 3]}, {"id": "13410", "type": [1, 0, 0, 0], "emotion": 5, "goal": [4, 3]}, {"id": "20019", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 15, 6, 3]}, {"id": "14778", "type": [1, 0, 0, 0], "emotion": 8, "goal": [11, 0, 1, 3]}, {"id": "187", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 9]}, {"id": "10959", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12686", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14291", "type": [1, 1, 0, 0], "emotion": 2, "goal": [18, 3]}, {"id": "14875", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13, 4, 3]}, {"id": "8431", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15, 10]}, {"id": "19870", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 11, 3]}, {"id": "2211", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 10]}, {"id": "16566", "type": [1, 1, 1, 1], "emotion": 1, "goal": [3, 4]}, {"id": "19412", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "1748", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 2]}, {"id": "18737", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "1701", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 5]}, {"id": "18678", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5036", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 20]}, {"id": "3678", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13, 4]}, {"id": "12719", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5761", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "9236", "type": [1, 1, 0, 0], "emotion": 2, "goal": [10, 3, 20]}, {"id": "5825", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 2]}, {"id": "8907", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 11]}, {"id": "12164", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6729", "type": [1, 0, 0, 0], "emotion": 3, "goal": [13, 3]}, {"id": "19775", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "3661", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "18159", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 3]}, {"id": "17791", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "949", "type": [1, 1, 0, 0], "emotion": 5, "goal": [15, 10, 6, 5, 4]}, {"id": "799", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 14, 10]}, {"id": "17020", "type": [1, 1, 0, 0], "emotion": 6, "goal": [13, 6, 15, 17]}, {"id": "8342", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 10]}, {"id": "15910", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "13205", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "5658", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "5066", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9173", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 5]}, {"id": "16150", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 13]}, {"id": "1638", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 0]}, {"id": "11663", "type": [1, 0, 0, 0], "emotion": 5, "goal": [15, 13, 3, 20]}, {"id": "4442", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 7]}, {"id": "3868", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 3, 4]}, {"id": "17830", "type": [1, 0, 0, 0], "emotion": 7, "goal": [11, 3, 0]}, {"id": "17483", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9507", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 11]}, {"id": "19568", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19026", "type": [1, 0, 0, 0], "emotion": 4, "goal": [1, 16]}, {"id": "10302", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "233", "type": [1, 0, 0, 0], "emotion": 4, "goal": [7]}, {"id": "4561", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "19668", "type": [1, 0, 1, 1], "emotion": 6, "goal": [4, 10, 3]}, {"id": "6993", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "9305", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6]}, {"id": "9464", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 0, 11]}, {"id": "9213", "type": [1, 0, 1, 0], "emotion": 4, "goal": [4, 12]}, {"id": "6491", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "7321", "type": [1, 0, 1, 0], "emotion": 2, "goal": [3, 15, 6, 20]}, {"id": "4097", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13, 4]}, {"id": "12069", "type": [1, 1, 0, 0], "emotion": 2, "goal": [20, 3]}, {"id": "5330", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13, 3]}, {"id": "19665", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "13952", "type": [1, 0, 1, 0], "emotion": 4, "goal": [18, 3]}, {"id": "15266", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15, 3]}, {"id": "9533", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "17811", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 16]}, {"id": "4340", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "13269", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "656", "type": [1, 0, 0, 0], "emotion": 2, "goal": [10, 20]}, {"id": "9784", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 10]}, {"id": "19791", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "18997", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "14780", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5282", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 0]}, {"id": "16284", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9, 3]}, {"id": "7490", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 3]}, {"id": "19612", "type": [1, 0, 0, 0], "emotion": 5, "goal": [3, 16]}, {"id": "11319", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "14504", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "19804", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "4649", "type": [1, 0, 1, 0], "emotion": 6, "goal": [17, 3]}, {"id": "13920", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17699", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "15305", "type": [1, 0, 0, 0], "emotion": 5, "goal": [3, 16]}, {"id": "7685", "type": [1, 0, 1, 0], "emotion": 1, "goal": [13, 2]}, {"id": "5421", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 3, 0, 11]}, {"id": "941", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 3, 4]}, {"id": "1426", "type": [1, 0, 0, 0], "emotion": 4, "goal": [18, 13, 3]}, {"id": "7927", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 5, 15, 18]}, {"id": "16527", "type": [1, 0, 0, 0], "emotion": 4, "goal": [11, 0]}, {"id": "6071", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 3]}, {"id": "13889", "type": [1, 1, 0, 0], "emotion": 5, "goal": [4, 5, 15, 3]}, {"id": "12526", "type": [1, 1, 0, 0], "emotion": 5, "goal": [4, 5, 6, 15, 12]}, {"id": "18932", "type": [1, 1, 0, 0], "emotion": 7, "goal": [7, 11]}, {"id": "11886", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12, 3]}, {"id": "19123", "type": [1, 1, 0, 0], "emotion": 1, "goal": [12, 10, 2]}, {"id": "5512", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3438", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4]}, {"id": "2282", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "16930", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 5, 4, 15, 3]}, {"id": "12562", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 13, 3]}, {"id": "9351", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "2030", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12331", "type": [1, 1, 0, 0], "emotion": 5, "goal": [4, 5, 6, 15]}, {"id": "5672", "type": [1, 0, 0, 0], "emotion": 4, "goal": [18, 13, 3]}, {"id": "10364", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 20]}, {"id": "14826", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12573", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 5, 15, 3]}, {"id": "16743", "type": [1, 1, 0, 0], "emotion": 5, "goal": [17, 16]}, {"id": "13225", "type": [1, 0, 0, 0], "emotion": 8, "goal": [1, 0]}, {"id": "4079", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 5, 6, 15, 3]}, {"id": "1655", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "430", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 5, 15]}, {"id": "5519", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 3]}, {"id": "3495", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 6, 15, 10]}, {"id": "3014", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "12809", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 10]}, {"id": "113", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 15]}, {"id": "8328", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "11075", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "16975", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "11451", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 12]}, {"id": "2492", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "12039", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 6, 15]}, {"id": "8514", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 15, 3]}, {"id": "10446", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "9885", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "1982", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 13, 4, 5]}, {"id": "3692", "type": [1, 1, 0, 0], "emotion": 4, "goal": [11, 4]}, {"id": "16932", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "5747", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 2]}, {"id": "7394", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "13291", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 16]}, {"id": "14343", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 3, 12]}, {"id": "12874", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16622", "type": [1, 1, 0, 0], "emotion": 7, "goal": [7, 11]}, {"id": "10038", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "6215", "type": [1, 0, 0, 0], "emotion": 7, "goal": [3, 7]}, {"id": "2158", "type": [1, 0, 1, 0], "emotion": 8, "goal": [18, 4]}, {"id": "17767", "type": [1, 0, 1, 1], "emotion": 2, "goal": [4, 6, 15]}, {"id": "1622", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 5]}, {"id": "13660", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "6113", "type": [1, 0, 1, 0], "emotion": 4, "goal": [12, 3]}, {"id": "9782", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3]}, {"id": "4152", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9]}, {"id": "16054", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 13]}, {"id": "2611", "type": [1, 0, 1, 0], "emotion": 1, "goal": [13, 3]}, {"id": "10604", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 3]}, {"id": "19661", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 3, 15]}, {"id": "9317", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 18, 11]}, {"id": "13354", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6772", "type": [1, 0, 0, 0], "emotion": 8, "goal": [1, 16]}, {"id": "19433", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "18629", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "16155", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "5548", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12529", "type": [1, 1, 0, 0], "emotion": 6, "goal": [13, 3, 9]}, {"id": "10879", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "19687", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "1070", "type": [1, 0, 0, 0], "emotion": 8, "goal": [12, 0]}, {"id": "4498", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "116", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 2]}, {"id": "16227", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "7940", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 4, 15]}, {"id": "9052", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9273", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 6, 15]}, {"id": "5669", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "15659", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 20]}, {"id": "62", "type": [1, 1, 0, 0], "emotion": 0, "goal": [1, 0]}, {"id": "10739", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13]}, {"id": "12477", "type": [1, 1, 0, 0], "emotion": 4, "goal": [9, 3]}, {"id": "17643", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "788", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 4, 3]}, {"id": "2263", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4959", "type": [1, 0, 0, 0], "emotion": 4, "goal": [8, 4, 3]}, {"id": "11515", "type": [1, 0, 1, 1], "emotion": 1, "goal": [12, 18]}, {"id": "12493", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1453", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9652", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 18]}, {"id": "17199", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3]}, {"id": "3674", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "18012", "type": [1, 0, 0, 0], "emotion": 6, "goal": [17, 13, 2]}, {"id": "17701", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13, 3]}, {"id": "2542", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 4, 3]}, {"id": "17055", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 3]}, {"id": "4157", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "930", "type": [1, 1, 0, 0], "emotion": 4, "goal": [8, 3]}, {"id": "12387", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 1, 13]}, {"id": "20150", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4, 3]}, {"id": "7218", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 15, 3]}, {"id": "14433", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "12937", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14010", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 13, 4, 15]}, {"id": "15508", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "5267", "type": [1, 1, 0, 0], "emotion": 3, "goal": [3, 16]}, {"id": "17974", "type": [1, 0, 1, 0], "emotion": 2, "goal": [3, 15, 6]}, {"id": "8753", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 13]}, {"id": "6130", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "3916", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15, 10]}, {"id": "14545", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 3, 15]}, {"id": "12384", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 10]}, {"id": "10055", "type": [1, 1, 0, 0], "emotion": 2, "goal": [20, 3]}, {"id": "8917", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "11413", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 0, 3]}, {"id": "18057", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "7489", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12351", "type": [1, 0, 0, 0], "emotion": 9, "goal": [12, 15]}, {"id": "6191", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 16]}, {"id": "15227", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 16]}, {"id": "3013", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "13976", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 15, 3]}, {"id": "17240", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17940", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13, 3]}, {"id": "10819", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "16302", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 3, 9]}, {"id": "10727", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "4550", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 11]}, {"id": "8151", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 2]}, {"id": "6011", "type": [1, 0, 0, 0], "emotion": 4, "goal": [1, 16]}, {"id": "8946", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "3035", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "17651", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "6956", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 20]}, {"id": "6324", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "631", "type": [1, 0, 1, 0], "emotion": 4, "goal": [10, 13, 3]}, {"id": "12021", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "886", "type": [1, 0, 0, 0], "emotion": 2, "goal": [4, 5, 6]}, {"id": "15288", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "12533", "type": [1, 1, 0, 0], "emotion": 5, "goal": [18, 4, 5, 15, 3]}, {"id": "19606", "type": [1, 1, 0, 0], "emotion": 4, "goal": [16, 13]}, {"id": "13094", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 13]}, {"id": "16335", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13, 3]}, {"id": "16119", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 5, 10]}, {"id": "15779", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 16]}, {"id": "9370", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 18, 3]}, {"id": "12524", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 15, 13, 3]}, {"id": "13124", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "18736", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "8479", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 6, 15]}, {"id": "7816", "type": [1, 0, 1, 0], "emotion": 5, "goal": [4, 6, 15, 14, 9]}, {"id": "9040", "type": [1, 1, 0, 0], "emotion": 6, "goal": [15, 5, 3]}, {"id": "1057", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "2036", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "14499", "type": [1, 0, 0, 0], "emotion": 4, "goal": [18, 13, 3, 8]}, {"id": "19732", "type": [1, 1, 0, 0], "emotion": 7, "goal": [13, 15]}, {"id": "968", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 13]}, {"id": "8137", "type": [1, 0, 1, 0], "emotion": 2, "goal": [7, 6]}, {"id": "18590", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 10, 15]}, {"id": "13003", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "10983", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 3]}, {"id": "4151", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "4263", "type": [1, 0, 0, 0], "emotion": 0, "goal": [11, 16]}, {"id": "1867", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "15280", "type": [1, 0, 1, 0], "emotion": 4, "goal": [12, 16]}, {"id": "16553", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "8130", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 16]}, {"id": "12357", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 5, 3]}, {"id": "14721", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9009", "type": [1, 0, 1, 1], "emotion": 4, "goal": [3, 16]}, {"id": "19616", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "310", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12]}, {"id": "762", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 5, 4]}, {"id": "16918", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "1335", "type": [1, 1, 0, 0], "emotion": 4, "goal": [16]}, {"id": "16864", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16799", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "4742", "type": [1, 0, 1, 0], "emotion": 9, "goal": [4, 16, 3]}, {"id": "14386", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6]}, {"id": "1385", "type": [1, 1, 0, 0], "emotion": 1, "goal": [18, 4, 13]}, {"id": "7962", "type": [1, 1, 0, 0], "emotion": 5, "goal": [13, 4, 15, 6]}, {"id": "6216", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "13433", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "15191", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "15321", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "10172", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "10222", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13, 6]}, {"id": "20084", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "8834", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 13]}, {"id": "6820", "type": [1, 1, 0, 0], "emotion": 4, "goal": [5, 15]}, {"id": "6530", "type": [1, 0, 1, 1], "emotion": 5, "goal": [18, 4, 3]}, {"id": "17363", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "3444", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 0, 3]}, {"id": "7342", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "7932", "type": [1, 1, 0, 0], "emotion": 2, "goal": [18, 13, 4, 5]}, {"id": "19722", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17499", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13]}, {"id": "6818", "type": [1, 1, 0, 0], "emotion": 4, "goal": [0, 9, 3, 1]}, {"id": "8329", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "19942", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12230", "type": [1, 1, 0, 0], "emotion": 4, "goal": [20, 11]}, {"id": "1412", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 3]}, {"id": "5947", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "1172", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9]}, {"id": "7006", "type": [1, 1, 0, 0], "emotion": 8, "goal": [12, 0, 1]}, {"id": "14556", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "7327", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 16]}, {"id": "11124", "type": [1, 0, 0, 0], "emotion": 4, "goal": [6, 3]}, {"id": "7474", "type": [1, 1, 0, 0], "emotion": 2, "goal": [9, 3, 6, 15]}, {"id": "16980", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "11716", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "8610", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "10858", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4]}, {"id": "18232", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "95", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "3085", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 5, 15]}, {"id": "19959", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 3, 15]}, {"id": "16501", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "1011", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "19488", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15, 3]}, {"id": "9190", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 15]}, {"id": "15768", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "11089", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 11]}, {"id": "5159", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "13836", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "15970", "type": [1, 0, 1, 1], "emotion": 4, "goal": [18, 13, 3]}, {"id": "14825", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 3]}, {"id": "19142", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "18778", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3959", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "9513", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15, 3]}, {"id": "6547", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "10247", "type": [1, 0, 0, 0], "emotion": 0, "goal": [13, 1, 3]}, {"id": "12443", "type": [1, 0, 1, 1], "emotion": 4, "goal": [15, 10, 3]}, {"id": "13874", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 14, 9, 3]}, {"id": "18922", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "3832", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "8841", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "17837", "type": [1, 0, 0, 0], "emotion": 1, "goal": [3, 16]}, {"id": "2502", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 2]}, {"id": "1330", "type": [1, 0, 1, 0], "emotion": 1, "goal": [15, 7]}, {"id": "10947", "type": [1, 1, 0, 0], "emotion": 0, "goal": [13, 0, 3]}, {"id": "2623", "type": [1, 1, 0, 0], "emotion": 0, "goal": [5, 0, 11]}, {"id": "3566", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 5, 6, 15]}, {"id": "3913", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "6804", "type": [1, 0, 1, 1], "emotion": 6, "goal": [13, 3]}, {"id": "17832", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "6858", "type": [1, 1, 0, 0], "emotion": 5, "goal": [4, 5, 6, 15, 13]}, {"id": "10847", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "10357", "type": [1, 0, 1, 1], "emotion": 2, "goal": [4, 13, 15, 20, 3]}, {"id": "18195", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 15, 20]}, {"id": "4748", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "9940", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 9]}, {"id": "12498", "type": [1, 0, 0, 0], "emotion": 4, "goal": [16]}, {"id": "6303", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 13, 15, 6]}, {"id": "12681", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16111", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 10]}, {"id": "4333", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "9368", "type": [1, 0, 1, 0], "emotion": 9, "goal": [12, 0]}, {"id": "7673", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "17254", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "1106", "type": [1, 1, 0, 0], "emotion": 4, "goal": [8, 15]}, {"id": "18527", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4]}, {"id": "2381", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 16]}, {"id": "15008", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 15]}, {"id": "15167", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10, 5, 4]}, {"id": "19216", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 15, 3, 20]}, {"id": "1634", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 4]}, {"id": "8621", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "20153", "type": [1, 1, 0, 0], "emotion": 1, "goal": [3, 15, 4, 13]}, {"id": "10150", "type": [1, 1, 0, 0], "emotion": 6, "goal": [13, 15, 3]}, {"id": "12590", "type": [1, 1, 0, 0], "emotion": 5, "goal": [18, 6, 15]}, {"id": "12794", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "7334", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "4762", "type": [1, 0, 0, 0], "emotion": 8, "goal": [0, 11, 1]}, {"id": "16296", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13, 3]}, {"id": "2764", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 12]}, {"id": "14206", "type": [1, 0, 1, 0], "emotion": 9, "goal": [13, 16]}, {"id": "7551", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "5562", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 6]}, {"id": "12434", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13, 3]}, {"id": "1724", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "9257", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "17448", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "103", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13]}, {"id": "7798", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "1875", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "11777", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "4852", "type": [1, 1, 0, 0], "emotion": 5, "goal": [4, 5, 6, 15]}, {"id": "4752", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "7818", "type": [1, 0, 1, 1], "emotion": 7, "goal": [4, 3, 1]}, {"id": "12096", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "16526", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "2853", "type": [1, 0, 0, 0], "emotion": 8, "goal": [12, 18]}, {"id": "7611", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 14]}, {"id": "5210", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "19087", "type": [1, 0, 1, 0], "emotion": 4, "goal": [18, 3, 11]}, {"id": "10293", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 5, 4]}, {"id": "13392", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 4]}, {"id": "9311", "type": [1, 1, 0, 0], "emotion": 5, "goal": [17, 13, 15, 6]}, {"id": "18967", "type": [1, 1, 0, 0], "emotion": 4, "goal": [16, 3]}, {"id": "16983", "type": [1, 0, 1, 0], "emotion": 4, "goal": [12, 4]}, {"id": "17005", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "18183", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "2436", "type": [1, 1, 0, 0], "emotion": 1, "goal": [8, 7, 15, 14]}, {"id": "14154", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "4191", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 15, 20]}, {"id": "14678", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "17348", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "14771", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "6108", "type": [1, 1, 0, 0], "emotion": 2, "goal": [12, 10]}, {"id": "17251", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1601", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 8]}, {"id": "18172", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "4440", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 10]}, {"id": "15542", "type": [1, 0, 1, 0], "emotion": 8, "goal": [12, 11, 0]}, {"id": "5694", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "7680", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 10]}, {"id": "5093", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "1097", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 8, 4]}, {"id": "13613", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12295", "type": [1, 1, 0, 0], "emotion": 5, "goal": [4, 5]}, {"id": "10221", "type": [1, 0, 1, 1], "emotion": 0, "goal": [13, 11, 1]}, {"id": "13317", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "12476", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13, 3]}, {"id": "17266", "type": [1, 0, 1, 1], "emotion": 0, "goal": [3, 16]}, {"id": "3558", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 9]}, {"id": "15077", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16682", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "13866", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 15, 3, 13]}, {"id": "14632", "type": [1, 0, 0, 0], "emotion": 4, "goal": [6, 15]}, {"id": "7549", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "1467", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 2, 3]}, {"id": "12958", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "8746", "type": [1, 0, 1, 1], "emotion": 2, "goal": [3, 4]}, {"id": "3938", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "14828", "type": [1, 1, 0, 0], "emotion": 5, "goal": [3, 18]}, {"id": "6041", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "14400", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14007", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 16]}, {"id": "10358", "type": [1, 0, 1, 0], "emotion": 4, "goal": [12, 3]}, {"id": "19886", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "10085", "type": [1, 1, 0, 0], "emotion": 5, "goal": [6, 15, 3]}, {"id": "6853", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "2792", "type": [1, 1, 0, 0], "emotion": 5, "goal": [6, 15, 18]}, {"id": "7333", "type": [1, 0, 1, 0], "emotion": 8, "goal": [13, 3, 1]}, {"id": "2685", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 6, 15]}, {"id": "19667", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "10924", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "4664", "type": [1, 1, 0, 0], "emotion": 5, "goal": [13, 16]}, {"id": "10864", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 0]}, {"id": "4312", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "983", "type": [1, 0, 1, 0], "emotion": 4, "goal": [14, 9]}, {"id": "14221", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 18, 3]}, {"id": "12678", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 0]}, {"id": "10704", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "18664", "type": [1, 1, 0, 0], "emotion": 4, "goal": [9, 13]}, {"id": "2575", "type": [1, 0, 0, 0], "emotion": 5, "goal": [18, 15, 3]}, {"id": "12959", "type": [1, 0, 1, 0], "emotion": 4, "goal": [18, 3]}, {"id": "6721", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "12997", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 6]}, {"id": "2935", "type": [1, 1, 0, 0], "emotion": 6, "goal": [4, 16]}, {"id": "9068", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "1038", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 15, 14]}, {"id": "16857", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "17946", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19465", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 6, 3]}, {"id": "2275", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 3]}, {"id": "7715", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "3093", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17531", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "13101", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 20]}, {"id": "1194", "type": [1, 0, 1, 1], "emotion": 4, "goal": [4, 20]}, {"id": "15545", "type": [1, 1, 0, 0], "emotion": 2, "goal": [10, 16]}, {"id": "3787", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 6]}, {"id": "14931", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "2691", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12]}, {"id": "15214", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 16]}, {"id": "4028", "type": [1, 1, 0, 0], "emotion": 6, "goal": [13, 17]}, {"id": "1000", "type": [1, 1, 0, 0], "emotion": 5, "goal": [3, 6]}, {"id": "10899", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "7707", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "14137", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12020", "type": [1, 0, 1, 0], "emotion": 2, "goal": [17, 3]}, {"id": "5301", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 13, 5, 15, 6, 3]}, {"id": "738", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 5]}, {"id": "2972", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 4]}, {"id": "12105", "type": [1, 1, 1, 1], "emotion": 8, "goal": [18, 11, 0]}, {"id": "11615", "type": [1, 0, 1, 1], "emotion": 4, "goal": [4, 16]}, {"id": "13451", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "9629", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "11650", "type": [1, 1, 0, 0], "emotion": 5, "goal": [13, 4, 15, 3, 20]}, {"id": "7302", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "11007", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "12362", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "6238", "type": [1, 0, 0, 0], "emotion": 0, "goal": [11, 0]}, {"id": "11888", "type": [1, 1, 0, 0], "emotion": 1, "goal": [4, 15, 3]}, {"id": "6029", "type": [1, 1, 1, 0], "emotion": 4, "goal": [13, 1]}, {"id": "13253", "type": [1, 0, 1, 0], "emotion": 6, "goal": [17, 3]}, {"id": "12445", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "6813", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "19764", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "7066", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "13176", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13]}, {"id": "19877", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "3737", "type": [1, 1, 0, 0], "emotion": 7, "goal": [13, 16]}, {"id": "458", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 10]}, {"id": "13665", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 15, 3]}, {"id": "8882", "type": [1, 0, 1, 0], "emotion": 2, "goal": [18, 6, 15]}, {"id": "19703", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 6]}, {"id": "3347", "type": [1, 0, 1, 0], "emotion": 4, "goal": [4, 13]}, {"id": "3322", "type": [1, 1, 0, 0], "emotion": 6, "goal": [13, 18, 5, 15, 20]}, {"id": "17457", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "7997", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9, 3]}, {"id": "10910", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12991", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 2]}, {"id": "1879", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "8974", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "1860", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 13]}, {"id": "14037", "type": [1, 0, 0, 0], "emotion": 0, "goal": [13, 0, 3, 11, 1]}, {"id": "15787", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12187", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 10]}, {"id": "9295", "type": [1, 0, 0, 0], "emotion": 2, "goal": [15, 10]}, {"id": "11539", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19820", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "4863", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "10459", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "13212", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 16]}, {"id": "13523", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 20]}, {"id": "277", "type": [1, 0, 0, 0], "emotion": 4, "goal": [4, 3]}, {"id": "14344", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17402", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "16290", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 13, 3]}, {"id": "3087", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 19, 2, 3]}, {"id": "13188", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "4968", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 5, 15, 13]}, {"id": "17833", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4600", "type": [1, 0, 0, 0], "emotion": 2, "goal": [18, 6]}, {"id": "3856", "type": [1, 0, 0, 0], "emotion": 4, "goal": [7, 13]}, {"id": "2848", "type": [1, 0, 0, 0], "emotion": 0, "goal": [11, 0]}, {"id": "1222", "type": [1, 1, 0, 0], "emotion": 1, "goal": [19, 13]}, {"id": "12974", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13]}, {"id": "5010", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 3]}, {"id": "644", "type": [1, 1, 0, 0], "emotion": 5, "goal": [17, 5, 4, 6]}, {"id": "20029", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1484", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 14]}, {"id": "16968", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "12113", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "17295", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 11]}, {"id": "9118", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1976", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 16]}, {"id": "10485", "type": [1, 1, 0, 0], "emotion": 4, "goal": [20, 3]}, {"id": "15355", "type": [1, 0, 1, 1], "emotion": 2, "goal": [3, 6]}, {"id": "14845", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 3]}, {"id": "9651", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "16011", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "2058", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 15, 13]}, {"id": "19864", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "1240", "type": [1, 0, 0, 0], "emotion": 4, "goal": [16]}, {"id": "1715", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "4032", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 3]}, {"id": "10387", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "2197", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "5417", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5776", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 5]}, {"id": "11621", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 9, 13]}, {"id": "14451", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "1215", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 5, 13]}, {"id": "6350", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "16337", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3261", "type": [1, 1, 0, 0], "emotion": 1, "goal": [3, 16]}, {"id": "7311", "type": [1, 0, 1, 1], "emotion": 2, "goal": [15, 6, 12]}, {"id": "358", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 15]}, {"id": "7601", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 3]}, {"id": "11389", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 3]}, {"id": "3244", "type": [1, 0, 0, 0], "emotion": 3, "goal": [0, 16]}, {"id": "2649", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 6, 15]}, {"id": "5032", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "18051", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 16]}, {"id": "8261", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 10]}, {"id": "5706", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9]}, {"id": "11578", "type": [1, 1, 0, 0], "emotion": 4, "goal": [19, 10, 3]}, {"id": "18412", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "18055", "type": [1, 1, 0, 0], "emotion": 5, "goal": [13, 15]}, {"id": "5944", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "13440", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 12, 13]}, {"id": "546", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "17438", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "17128", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3480", "type": [1, 1, 0, 0], "emotion": 6, "goal": [13, 6, 15]}, {"id": "19834", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 13]}, {"id": "5876", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "9076", "type": [1, 0, 0, 0], "emotion": 1, "goal": [14, 9, 3]}, {"id": "12554", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 3]}, {"id": "4403", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "1814", "type": [1, 0, 0, 0], "emotion": 4, "goal": [4, 15, 20]}, {"id": "3817", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 0, 3, 11]}, {"id": "17827", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15, 3]}, {"id": "16660", "type": [1, 0, 1, 0], "emotion": 0, "goal": [3, 11]}, {"id": "11100", "type": [1, 1, 0, 0], "emotion": 2, "goal": [10, 13, 15, 4]}, {"id": "19611", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6786", "type": [1, 0, 0, 0], "emotion": 2, "goal": [18, 15, 3]}, {"id": "11689", "type": [1, 0, 1, 1], "emotion": 4, "goal": [4, 15, 3]}, {"id": "14114", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "11522", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19550", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "2235", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "6298", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 11, 1]}, {"id": "10456", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13139", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 20]}, {"id": "5777", "type": [1, 0, 0, 0], "emotion": 2, "goal": [2, 16]}, {"id": "475", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "12419", "type": [1, 1, 0, 0], "emotion": 1, "goal": [12, 9, 3]}, {"id": "14784", "type": [1, 0, 0, 0], "emotion": 10, "goal": [3, 12, 19]}, {"id": "15605", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 2]}, {"id": "1234", "type": [1, 0, 1, 1], "emotion": 4, "goal": [4, 11, 5]}, {"id": "8249", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1404", "type": [1, 0, 1, 1], "emotion": 1, "goal": [13, 4, 3]}, {"id": "393", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "2435", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13]}, {"id": "2840", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 4]}, {"id": "11838", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13063", "type": [1, 0, 0, 0], "emotion": 8, "goal": [0, 3, 12]}, {"id": "8649", "type": [1, 0, 0, 0], "emotion": 4, "goal": [20, 3]}, {"id": "3870", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 0]}, {"id": "3243", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 5, 3]}, {"id": "18793", "type": [1, 1, 0, 0], "emotion": 4, "goal": [19, 3]}, {"id": "5685", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 10]}, {"id": "2299", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "19893", "type": [1, 0, 1, 1], "emotion": 4, "goal": [19, 13, 3]}, {"id": "236", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "11842", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 4]}, {"id": "3955", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 15, 4]}, {"id": "16538", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "3901", "type": [1, 0, 1, 0], "emotion": 5, "goal": [6, 15, 3]}, {"id": "2989", "type": [1, 1, 1, 1], "emotion": 2, "goal": [4, 15, 3]}, {"id": "5191", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "18602", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13522", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "12394", "type": [1, 0, 0, 0], "emotion": 2, "goal": [20, 3, 13]}, {"id": "10322", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4, 3]}, {"id": "19371", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 20, 3]}, {"id": "11568", "type": [1, 0, 1, 1], "emotion": 2, "goal": [5, 15]}, {"id": "6342", "type": [1, 1, 0, 0], "emotion": 0, "goal": [3, 0, 12, 1, 11]}, {"id": "18960", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "2001", "type": [1, 0, 0, 0], "emotion": 4, "goal": [8, 3]}, {"id": "2601", "type": [1, 1, 0, 0], "emotion": 5, "goal": [17, 6, 5]}, {"id": "18302", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "13074", "type": [1, 1, 0, 0], "emotion": 4, "goal": [16]}, {"id": "14795", "type": [1, 1, 0, 0], "emotion": 1, "goal": [4, 3]}, {"id": "15232", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 15]}, {"id": "1478", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13, 4]}, {"id": "10250", "type": [1, 1, 0, 0], "emotion": 10, "goal": [3, 15]}, {"id": "18215", "type": [1, 1, 0, 0], "emotion": 6, "goal": [13, 17]}, {"id": "1712", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "3314", "type": [1, 0, 0, 0], "emotion": 8, "goal": [12, 0, 11]}, {"id": "16079", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3]}, {"id": "4010", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3176", "type": [1, 1, 0, 0], "emotion": 4, "goal": [16]}, {"id": "10740", "type": [1, 0, 1, 0], "emotion": 6, "goal": [17, 3]}, {"id": "15901", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 10]}, {"id": "9204", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 5, 6, 15]}, {"id": "3230", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15, 13]}, {"id": "7597", "type": [1, 0, 1, 0], "emotion": 4, "goal": [12, 9]}, {"id": "16419", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 11, 0]}, {"id": "19326", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "11808", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 15]}, {"id": "16887", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "17048", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9589", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "13754", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3720", "type": [1, 0, 0, 0], "emotion": 4, "goal": [18, 13]}, {"id": "7482", "type": [1, 0, 1, 0], "emotion": 7, "goal": [15, 13]}, {"id": "12289", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "4523", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "4166", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6304", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 12, 9]}, {"id": "3781", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 6, 15]}, {"id": "10410", "type": [1, 1, 0, 0], "emotion": 0, "goal": [13, 3, 1]}, {"id": "20078", "type": [1, 0, 0, 0], "emotion": 7, "goal": [7, 19, 3]}, {"id": "13713", "type": [1, 0, 1, 0], "emotion": 8, "goal": [13, 3]}, {"id": "790", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 4, 3, 5]}, {"id": "4862", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 17]}, {"id": "11603", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5557", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 13]}, {"id": "5154", "type": [1, 0, 0, 0], "emotion": 2, "goal": [4, 5, 3]}, {"id": "18892", "type": [1, 1, 0, 0], "emotion": 8, "goal": [12, 0, 11]}, {"id": "8632", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "17622", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16416", "type": [1, 1, 0, 0], "emotion": 1, "goal": [3, 9, 14, 13]}, {"id": "13112", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9, 14]}, {"id": "19259", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15, 3]}, {"id": "11429", "type": [1, 0, 1, 0], "emotion": 5, "goal": [17, 6, 15]}, {"id": "17472", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 11]}, {"id": "3434", "type": [1, 0, 0, 0], "emotion": 4, "goal": [0, 11]}, {"id": "6785", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "15318", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "3677", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13]}, {"id": "20093", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "6728", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "7180", "type": [1, 1, 0, 0], "emotion": 4, "goal": [19, 16]}, {"id": "19541", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 3, 13]}, {"id": "16120", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "7323", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 11, 1]}, {"id": "12813", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "7269", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "10182", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 5, 15]}, {"id": "9161", "type": [1, 1, 0, 0], "emotion": 5, "goal": [18, 13, 4, 5, 6, 15]}, {"id": "2427", "type": [1, 0, 1, 1], "emotion": 2, "goal": [5, 6, 15]}, {"id": "18441", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "9449", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 13]}, {"id": "15119", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "7819", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "10545", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 6]}, {"id": "12019", "type": [1, 1, 0, 0], "emotion": 5, "goal": [18, 3, 17]}, {"id": "11555", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 8, 3, 9]}, {"id": "6538", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 1]}, {"id": "12239", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "15061", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17451", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "8640", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3616", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13]}, {"id": "8457", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 20]}, {"id": "1892", "type": [1, 1, 0, 0], "emotion": 5, "goal": [13, 10]}, {"id": "13456", "type": [1, 1, 0, 0], "emotion": 8, "goal": [0, 11, 1]}, {"id": "3957", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 12, 1]}, {"id": "19973", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "14815", "type": [1, 1, 0, 0], "emotion": 0, "goal": [4, 11, 0, 3]}, {"id": "13940", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 13, 15, 3]}, {"id": "3568", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15]}, {"id": "9107", "type": [1, 0, 0, 0], "emotion": 1, "goal": [3, 15, 4]}, {"id": "3345", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "1578", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 3]}, {"id": "2618", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 16]}, {"id": "12279", "type": [1, 0, 1, 1], "emotion": 8, "goal": [3, 0]}, {"id": "15333", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 16]}, {"id": "169", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 11]}, {"id": "19038", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "7712", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6]}, {"id": "19377", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "2172", "type": [1, 0, 0, 0], "emotion": 4, "goal": [10, 3]}, {"id": "9028", "type": [1, 0, 1, 0], "emotion": 2, "goal": [4, 10]}, {"id": "3161", "type": [1, 1, 1, 1], "emotion": 4, "goal": [13, 9, 3]}, {"id": "6552", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "15168", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "15816", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "15646", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 3]}, {"id": "2307", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5152", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "10688", "type": [1, 1, 0, 0], "emotion": 4, "goal": [2, 7, 3]}, {"id": "10777", "type": [1, 0, 1, 0], "emotion": 8, "goal": [3, 12]}, {"id": "11965", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "4176", "type": [1, 1, 0, 0], "emotion": 0, "goal": [4, 15, 3]}, {"id": "16049", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 4, 13]}, {"id": "7116", "type": [1, 0, 0, 0], "emotion": 4, "goal": [20, 0]}, {"id": "3960", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 12, 3]}, {"id": "11230", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "549", "type": [1, 0, 0, 0], "emotion": 1, "goal": [7, 6]}, {"id": "7722", "type": [1, 1, 0, 0], "emotion": 8, "goal": [11, 13]}, {"id": "15384", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "18344", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 3]}, {"id": "11182", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 13, 10]}, {"id": "14970", "type": [1, 1, 0, 0], "emotion": 7, "goal": [4, 3]}, {"id": "3367", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "7664", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5581", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "11585", "type": [1, 1, 0, 0], "emotion": 5, "goal": [18, 15, 20]}, {"id": "16529", "type": [1, 0, 1, 0], "emotion": 4, "goal": [11, 0]}, {"id": "3001", "type": [1, 0, 1, 1], "emotion": 5, "goal": [15, 13]}, {"id": "9452", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "17803", "type": [1, 0, 0, 0], "emotion": 9, "goal": [3, 13]}, {"id": "2083", "type": [1, 1, 0, 0], "emotion": 7, "goal": [13, 3, 15]}, {"id": "13682", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "18697", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5948", "type": [1, 0, 1, 0], "emotion": 2, "goal": [18, 13, 6]}, {"id": "14975", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3, 9]}, {"id": "10713", "type": [1, 1, 0, 0], "emotion": 2, "goal": [11, 16]}, {"id": "7281", "type": [1, 1, 0, 0], "emotion": 0, "goal": [3, 11]}, {"id": "6084", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "253", "type": [1, 1, 0, 0], "emotion": 7, "goal": [7]}, {"id": "13062", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "7635", "type": [1, 0, 1, 0], "emotion": 7, "goal": [7, 3, 4]}, {"id": "3088", "type": [1, 1, 0, 0], "emotion": 6, "goal": [13, 9, 3]}, {"id": "16422", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13536", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "7221", "type": [1, 0, 0, 0], "emotion": 4, "goal": [9, 16]}, {"id": "14633", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "2626", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 11]}, {"id": "7832", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "17162", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "11725", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9899", "type": [1, 1, 0, 0], "emotion": 2, "goal": [10, 6]}, {"id": "6358", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "17874", "type": [1, 0, 0, 0], "emotion": 4, "goal": [0, 3]}, {"id": "1729", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 18]}, {"id": "16073", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6]}, {"id": "7763", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "14565", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "11241", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "11825", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9364", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "1169", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "14612", "type": [1, 1, 0, 0], "emotion": 7, "goal": [19, 12]}, {"id": "1131", "type": [1, 0, 0, 0], "emotion": 8, "goal": [12, 0]}, {"id": "17132", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5003", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 5, 6, 15, 20]}, {"id": "4629", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "7610", "type": [1, 1, 0, 0], "emotion": 5, "goal": [13, 11]}, {"id": "13084", "type": [1, 0, 1, 0], "emotion": 4, "goal": [18, 3]}, {"id": "2247", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16393", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 6, 15, 3]}, {"id": "5789", "type": [1, 0, 0, 0], "emotion": 0, "goal": [3, 0]}, {"id": "11467", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 1]}, {"id": "19947", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "19170", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "4679", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "7809", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 13, 5, 6, 15]}, {"id": "7463", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "4194", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "8845", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4]}, {"id": "7507", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5674", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3, 16]}, {"id": "2022", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "10806", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "17526", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "16603", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 17]}, {"id": "3825", "type": [1, 1, 0, 0], "emotion": 8, "goal": [0, 11]}, {"id": "10969", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "13513", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 15, 3]}, {"id": "5829", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14917", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 1, 3]}, {"id": "11683", "type": [1, 1, 0, 0], "emotion": 4, "goal": [16]}, {"id": "16189", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "19353", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6]}, {"id": "9682", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "1881", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "3846", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13, 11]}, {"id": "12267", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 10]}, {"id": "8486", "type": [1, 0, 0, 0], "emotion": 5, "goal": [4, 3]}, {"id": "1430", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 0, 1]}, {"id": "10755", "type": [1, 0, 0, 0], "emotion": 4, "goal": [1, 3]}, {"id": "5689", "type": [1, 0, 1, 1], "emotion": 4, "goal": [18, 12, 11]}, {"id": "15850", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9395", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "6525", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "9399", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 15]}, {"id": "16209", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 3]}, {"id": "7208", "type": [1, 0, 1, 0], "emotion": 2, "goal": [4, 6, 3]}, {"id": "2606", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "1735", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "3980", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "7778", "type": [1, 1, 0, 0], "emotion": 8, "goal": [12, 3, 11]}, {"id": "5064", "type": [1, 0, 0, 0], "emotion": 4, "goal": [0, 9, 3]}, {"id": "6798", "type": [1, 1, 0, 0], "emotion": 5, "goal": [18, 13, 3]}, {"id": "3501", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "8145", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "7275", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15, 3]}, {"id": "2776", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "8982", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 4]}, {"id": "10347", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "10115", "type": [1, 0, 1, 0], "emotion": 1, "goal": [20, 3, 4]}, {"id": "19897", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3, 13]}, {"id": "4513", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "4540", "type": [1, 1, 0, 0], "emotion": 6, "goal": [12, 13, 9]}, {"id": "4484", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 4, 13]}, {"id": "3123", "type": [1, 0, 0, 0], "emotion": 0, "goal": [14, 1, 13]}, {"id": "1476", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 5, 15]}, {"id": "6212", "type": [1, 0, 1, 0], "emotion": 4, "goal": [18, 16]}, {"id": "16130", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "15847", "type": [1, 0, 1, 0], "emotion": 2, "goal": [12, 3]}, {"id": "5165", "type": [1, 1, 0, 0], "emotion": 4, "goal": [5, 3]}, {"id": "10737", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "9421", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5466", "type": [1, 1, 0, 0], "emotion": 4, "goal": [19, 12]}, {"id": "4816", "type": [1, 1, 0, 0], "emotion": 9, "goal": [4, 6, 15]}, {"id": "159", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 2]}, {"id": "2380", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 2]}, {"id": "11586", "type": [1, 0, 1, 1], "emotion": 5, "goal": [17, 4, 5, 15]}, {"id": "5047", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 3]}, {"id": "9715", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 13, 3]}, {"id": "13162", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "12988", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "19779", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5703", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 5]}, {"id": "2687", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "15270", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 15]}, {"id": "16731", "type": [1, 1, 0, 0], "emotion": 10, "goal": [7, 3]}, {"id": "8094", "type": [1, 1, 0, 0], "emotion": 4, "goal": [9, 3]}, {"id": "18212", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 16]}, {"id": "3821", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "15290", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 16]}, {"id": "19197", "type": [1, 0, 1, 0], "emotion": 5, "goal": [19, 16]}, {"id": "11806", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 4]}, {"id": "4293", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 16]}, {"id": "4948", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 5, 4, 15]}, {"id": "6695", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 5, 15]}, {"id": "15152", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "8081", "type": [1, 1, 0, 0], "emotion": 5, "goal": [5, 15, 10]}, {"id": "1290", "type": [1, 0, 0, 0], "emotion": 7, "goal": [14]}, {"id": "17629", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 4]}, {"id": "19251", "type": [1, 0, 1, 0], "emotion": 2, "goal": [19, 4, 3, 15]}, {"id": "10925", "type": [1, 1, 0, 0], "emotion": 8, "goal": [8, 0, 11]}, {"id": "19171", "type": [1, 0, 0, 0], "emotion": 2, "goal": [3, 16]}, {"id": "14887", "type": [1, 0, 1, 0], "emotion": 1, "goal": [15, 3]}, {"id": "16140", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4196", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 19]}, {"id": "14452", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "3897", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 3, 11]}, {"id": "7866", "type": [1, 0, 0, 0], "emotion": 2, "goal": [3, 4]}, {"id": "5273", "type": [1, 0, 1, 0], "emotion": 3, "goal": [0, 3]}, {"id": "18804", "type": [1, 0, 1, 0], "emotion": 4, "goal": [8, 3]}, {"id": "11579", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10, 3]}, {"id": "8058", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16298", "type": [1, 0, 1, 1], "emotion": 5, "goal": [18, 3]}, {"id": "1551", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 5, 6]}, {"id": "4000", "type": [1, 0, 0, 0], "emotion": 4, "goal": [15, 3]}, {"id": "3548", "type": [1, 0, 1, 0], "emotion": 2, "goal": [15, 6, 10]}, {"id": "15765", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "12501", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "8031", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 2, 3]}, {"id": "4177", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "15817", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 19, 20]}, {"id": "17213", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 3]}, {"id": "7725", "type": [1, 0, 0, 0], "emotion": 1, "goal": [4, 16]}, {"id": "3366", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9, 3]}, {"id": "5533", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 3]}, {"id": "19961", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4856", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 15]}, {"id": "8490", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 16]}, {"id": "8783", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 16]}, {"id": "18955", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 6]}, {"id": "11583", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 5, 15]}, {"id": "12576", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "7041", "type": [1, 0, 1, 1], "emotion": 4, "goal": [3, 16]}, {"id": "711", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 4, 15]}, {"id": "15734", "type": [1, 0, 0, 0], "emotion": 2, "goal": [15, 6, 10]}, {"id": "12641", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 2, 8, 3]}, {"id": "1847", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 4, 5]}, {"id": "6952", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 16]}, {"id": "2503", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "13726", "type": [1, 0, 0, 0], "emotion": 4, "goal": [20, 3]}, {"id": "5199", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "716", "type": [1, 1, 0, 0], "emotion": 1, "goal": [18, 4, 5]}, {"id": "10441", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 15, 12, 13]}, {"id": "15869", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1266", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 4, 12]}, {"id": "17235", "type": [1, 0, 0, 0], "emotion": 4, "goal": [6, 15]}, {"id": "17519", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 14]}, {"id": "11479", "type": [1, 0, 1, 1], "emotion": 3, "goal": [18, 0, 3, 11]}, {"id": "16881", "type": [1, 0, 0, 0], "emotion": 4, "goal": [9, 3]}, {"id": "18862", "type": [1, 1, 0, 0], "emotion": 2, "goal": [18, 6, 3]}, {"id": "11978", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 5, 15]}, {"id": "18505", "type": [1, 0, 0, 0], "emotion": 4, "goal": [4, 3]}, {"id": "1349", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "7521", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "15380", "type": [1, 0, 1, 1], "emotion": 4, "goal": [4, 13]}, {"id": "1698", "type": [1, 1, 0, 0], "emotion": 4, "goal": [8, 16]}, {"id": "18471", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "10502", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "53", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6]}, {"id": "9834", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 6]}, {"id": "10668", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 20]}, {"id": "6949", "type": [1, 0, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "13443", "type": [1, 0, 0, 0], "emotion": 1, "goal": [3, 16]}, {"id": "10706", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "17530", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "551", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13, 4]}, {"id": "7944", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 5, 6, 15]}, {"id": "17394", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1275", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "5637", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13, 3]}, {"id": "5993", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "11020", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 13, 4]}, {"id": "17045", "type": [1, 1, 0, 0], "emotion": 2, "goal": [19, 6, 15, 10]}, {"id": "5911", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "2342", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 3]}, {"id": "12628", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 10, 9]}, {"id": "5955", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "16424", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 15, 12]}, {"id": "1528", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 16]}, {"id": "20130", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17252", "type": [1, 0, 0, 0], "emotion": 8, "goal": [0, 3]}, {"id": "2134", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 11]}, {"id": "7412", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13]}, {"id": "17303", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 0]}, {"id": "2602", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "15838", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "11627", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 5]}, {"id": "9331", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 8, 3, 0]}, {"id": "9866", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 16]}, {"id": "17560", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "11556", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "16964", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 4, 15, 10]}, {"id": "11916", "type": [1, 1, 0, 0], "emotion": 7, "goal": [15, 3]}, {"id": "19441", "type": [1, 0, 0, 0], "emotion": 0, "goal": [13, 0, 11]}, {"id": "8028", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "7433", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15, 4]}, {"id": "18495", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "17765", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 3, 5]}, {"id": "18371", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 10]}, {"id": "13558", "type": [1, 0, 0, 0], "emotion": 6, "goal": [13, 17]}, {"id": "15896", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "19899", "type": [1, 0, 1, 0], "emotion": 7, "goal": [0, 3]}, {"id": "8819", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 16]}, {"id": "9095", "type": [1, 0, 1, 1], "emotion": 2, "goal": [4, 5, 15, 3]}, {"id": "19822", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13]}, {"id": "19315", "type": [1, 1, 0, 0], "emotion": 0, "goal": [3, 11, 1]}, {"id": "17979", "type": [1, 0, 0, 0], "emotion": 1, "goal": [15, 10, 3]}, {"id": "11365", "type": [1, 0, 1, 0], "emotion": 2, "goal": [18, 13, 11]}, {"id": "8076", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17617", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "10099", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 5, 6, 15]}, {"id": "9424", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13, 3]}, {"id": "3823", "type": [1, 0, 0, 0], "emotion": 0, "goal": [13, 0, 4]}, {"id": "926", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "6255", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "18329", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12710", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 15]}, {"id": "12872", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9]}, {"id": "17102", "type": [1, 1, 1, 1], "emotion": 2, "goal": [13, 5, 15, 3]}, {"id": "9867", "type": [1, 1, 0, 0], "emotion": 4, "goal": [1, 16]}, {"id": "16132", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "18810", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "5126", "type": [1, 0, 0, 0], "emotion": 2, "goal": [15, 5, 3]}, {"id": "16146", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15, 3]}, {"id": "17230", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "19381", "type": [1, 0, 1, 1], "emotion": 4, "goal": [3, 16]}, {"id": "12857", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 2]}, {"id": "18637", "type": [1, 0, 1, 0], "emotion": 4, "goal": [6, 15]}, {"id": "15343", "type": [1, 0, 1, 1], "emotion": 5, "goal": [17, 18]}, {"id": "3658", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "177", "type": [1, 1, 0, 0], "emotion": 2, "goal": [11, 1]}, {"id": "4588", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15, 17]}, {"id": "10918", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "12909", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 16]}, {"id": "12650", "type": [1, 0, 1, 0], "emotion": 2, "goal": [16]}, {"id": "7786", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "5183", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 13, 6, 15]}, {"id": "15795", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "913", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "16480", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "14680", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4501", "type": [1, 0, 0, 0], "emotion": 8, "goal": [3, 11]}, {"id": "7112", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "6670", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 5, 6, 15]}, {"id": "19880", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 11]}, {"id": "17382", "type": [1, 0, 1, 0], "emotion": 2, "goal": [3, 13]}, {"id": "2368", "type": [1, 0, 1, 0], "emotion": 4, "goal": [1, 16]}, {"id": "12382", "type": [1, 0, 0, 0], "emotion": 5, "goal": [15, 6, 4, 20]}, {"id": "16325", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 13, 3]}, {"id": "11235", "type": [1, 0, 0, 0], "emotion": 2, "goal": [12, 11]}, {"id": "9737", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 13]}, {"id": "12272", "type": [1, 0, 1, 0], "emotion": 5, "goal": [4, 16]}, {"id": "17826", "type": [1, 0, 0, 0], "emotion": 0, "goal": [1, 11, 3, 0]}, {"id": "4237", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "17137", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 4, 11]}, {"id": "17079", "type": [1, 1, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5411", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 10]}, {"id": "8335", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 10]}, {"id": "2788", "type": [1, 0, 1, 0], "emotion": 0, "goal": [1, 0]}, {"id": "6195", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 13]}, {"id": "9648", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 1]}, {"id": "7979", "type": [1, 0, 1, 0], "emotion": 2, "goal": [4, 5]}, {"id": "6141", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "19070", "type": [1, 1, 0, 0], "emotion": 6, "goal": [13, 10]}, {"id": "6381", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "17866", "type": [1, 1, 0, 0], "emotion": 4, "goal": [16]}, {"id": "17967", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "11591", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15, 14, 9, 20]}, {"id": "14202", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12, 3]}, {"id": "8874", "type": [1, 1, 0, 0], "emotion": 4, "goal": [8, 3]}, {"id": "6907", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 16]}, {"id": "11203", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "16522", "type": [1, 0, 0, 0], "emotion": 0, "goal": [11, 16]}, {"id": "18611", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14883", "type": [1, 1, 0, 0], "emotion": 5, "goal": [17, 13, 4, 15, 3]}, {"id": "10793", "type": [1, 0, 1, 0], "emotion": 4, "goal": [12, 16]}, {"id": "17928", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12948", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4739", "type": [1, 0, 1, 0], "emotion": 7, "goal": [3, 16, 7]}, {"id": "8493", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15, 3]}, {"id": "1640", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 6]}, {"id": "18262", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "1468", "type": [1, 0, 0, 0], "emotion": 1, "goal": [12, 14, 9]}, {"id": "9037", "type": [1, 0, 0, 0], "emotion": 10, "goal": [12, 3]}, {"id": "11947", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "3329", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 15, 4]}, {"id": "3472", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "7134", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 11]}, {"id": "27", "type": [1, 0, 1, 0], "emotion": 4, "goal": [8, 3]}, {"id": "19016", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "11461", "type": [1, 1, 0, 0], "emotion": 2, "goal": [11, 18, 4]}, {"id": "72", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 4]}, {"id": "4670", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 1]}, {"id": "4939", "type": [1, 0, 0, 0], "emotion": 5, "goal": [13, 4, 5, 6, 15, 3]}, {"id": "3492", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 11]}, {"id": "10680", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "17181", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14919", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "16306", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "19559", "type": [1, 0, 1, 0], "emotion": 4, "goal": [15, 3]}, {"id": "14782", "type": [1, 1, 0, 0], "emotion": 5, "goal": [18, 20, 3]}, {"id": "868", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "963", "type": [1, 1, 0, 0], "emotion": 1, "goal": [7, 8]}, {"id": "282", "type": [1, 1, 0, 0], "emotion": 2, "goal": [12, 11]}, {"id": "7784", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "3202", "type": [1, 0, 0, 0], "emotion": 7, "goal": [13, 3, 0]}, {"id": "3827", "type": [1, 0, 1, 0], "emotion": 4, "goal": [15, 3, 16]}, {"id": "7666", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "14042", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 15, 3]}, {"id": "6691", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13, 15]}, {"id": "8105", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13]}, {"id": "18188", "type": [1, 1, 0, 0], "emotion": 9, "goal": [13, 3]}, {"id": "2701", "type": [1, 0, 0, 0], "emotion": 0, "goal": [1, 13]}, {"id": "19096", "type": [1, 0, 0, 0], "emotion": 8, "goal": [0, 1]}, {"id": "3746", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 0]}, {"id": "16265", "type": [1, 1, 0, 0], "emotion": 1, "goal": [15, 3, 5, 4]}, {"id": "4483", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "17100", "type": [1, 0, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "7820", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 15]}, {"id": "13830", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13995", "type": [1, 0, 1, 0], "emotion": 4, "goal": [4, 13, 3]}, {"id": "6602", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13, 3]}, {"id": "13956", "type": [1, 1, 0, 0], "emotion": 1, "goal": [0, 9, 3, 15]}, {"id": "2906", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 6, 15]}, {"id": "19547", "type": [1, 0, 0, 0], "emotion": 1, "goal": [0, 14, 9, 3]}, {"id": "14365", "type": [1, 0, 1, 1], "emotion": 2, "goal": [3, 4, 15]}, {"id": "15169", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "17760", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 13, 15, 3]}, {"id": "1417", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 4, 5, 3]}, {"id": "7217", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "2793", "type": [1, 0, 0, 0], "emotion": 2, "goal": [17, 9]}, {"id": "7847", "type": [1, 0, 0, 0], "emotion": 5, "goal": [15, 5]}, {"id": "8555", "type": [1, 1, 0, 0], "emotion": 0, "goal": [3, 11, 1]}, {"id": "6162", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 12]}, {"id": "9770", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "4487", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "152", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 11]}, {"id": "6321", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 15]}, {"id": "16345", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 5, 15, 3]}, {"id": "9542", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "3260", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "13770", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13]}, {"id": "10890", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3, 11]}, {"id": "15326", "type": [1, 0, 1, 0], "emotion": 6, "goal": [3, 16]}, {"id": "16686", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "4082", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "7845", "type": [1, 0, 0, 0], "emotion": 2, "goal": [3, 20]}, {"id": "3274", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "11629", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12076", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "14215", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "6783", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "14415", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 6, 3]}, {"id": "6334", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4524", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "12125", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 13]}, {"id": "607", "type": [1, 0, 1, 1], "emotion": 6, "goal": [8, 3, 1]}, {"id": "19740", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15, 13]}, {"id": "13705", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "2785", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 16]}, {"id": "13152", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "7711", "type": [1, 0, 0, 0], "emotion": 1, "goal": [18, 11]}, {"id": "11496", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "15735", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "18769", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 12]}, {"id": "3091", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15, 10, 20]}, {"id": "11828", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 10, 3]}, {"id": "8336", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 13]}, {"id": "3600", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "335", "type": [1, 1, 0, 0], "emotion": 4, "goal": [10, 5]}, {"id": "10642", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "2639", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "4256", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 12]}, {"id": "15808", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "18824", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "12891", "type": [1, 0, 1, 1], "emotion": 4, "goal": [3, 16]}, {"id": "15604", "type": [1, 0, 1, 0], "emotion": 4, "goal": [12, 13]}, {"id": "6121", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "15919", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "18642", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 13]}, {"id": "4603", "type": [1, 1, 0, 0], "emotion": 6, "goal": [4, 3]}, {"id": "2944", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "13689", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "4066", "type": [1, 0, 1, 1], "emotion": 5, "goal": [18, 4, 3]}, {"id": "1736", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "7366", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19546", "type": [1, 0, 0, 0], "emotion": 2, "goal": [15, 6, 3]}, {"id": "12850", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "18539", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1494", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "3920", "type": [1, 0, 1, 0], "emotion": 2, "goal": [3, 11]}, {"id": "3862", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "594", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13, 3]}, {"id": "4848", "type": [1, 1, 1, 0], "emotion": 1, "goal": [15, 10, 3]}, {"id": "7124", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "2694", "type": [1, 1, 0, 0], "emotion": 7, "goal": [7, 13, 3]}, {"id": "19904", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "12771", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15, 3]}, {"id": "354", "type": [1, 0, 0, 0], "emotion": 4, "goal": [11, 3]}, {"id": "9077", "type": [1, 0, 0, 0], "emotion": 3, "goal": [0, 1]}, {"id": "8948", "type": [1, 0, 0, 0], "emotion": 4, "goal": [9, 3]}, {"id": "16235", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 3]}, {"id": "1102", "type": [1, 1, 0, 0], "emotion": 8, "goal": [0, 13]}, {"id": "836", "type": [1, 0, 1, 1], "emotion": 2, "goal": [5, 17]}, {"id": "1154", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "775", "type": [1, 1, 0, 0], "emotion": 7, "goal": [14]}, {"id": "12975", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 16]}, {"id": "2255", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13]}, {"id": "9654", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13]}, {"id": "15145", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "10903", "type": [1, 0, 0, 0], "emotion": 4, "goal": [6, 10, 15]}, {"id": "3005", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 3]}, {"id": "11098", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "410", "type": [1, 1, 0, 0], "emotion": 4, "goal": [8, 3, 15]}, {"id": "8880", "type": [1, 0, 1, 0], "emotion": 2, "goal": [20, 18]}, {"id": "15968", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 6]}, {"id": "9974", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 0, 3]}, {"id": "12131", "type": [1, 0, 0, 0], "emotion": 7, "goal": [7, 11]}, {"id": "13064", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3]}, {"id": "5098", "type": [1, 1, 0, 0], "emotion": 1, "goal": [5, 3, 0]}, {"id": "10639", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6]}, {"id": "11619", "type": [1, 0, 0, 0], "emotion": 0, "goal": [14, 11, 1]}, {"id": "7085", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 13, 3]}, {"id": "12713", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "18550", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "16976", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 12]}, {"id": "10461", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 12]}, {"id": "9404", "type": [1, 1, 0, 0], "emotion": 4, "goal": [10]}, {"id": "11109", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 11]}, {"id": "5051", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 10]}, {"id": "16982", "type": [1, 0, 0, 0], "emotion": 0, "goal": [3, 11]}, {"id": "8467", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13]}, {"id": "4488", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "10754", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "3078", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 13]}, {"id": "17193", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "10285", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "11329", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "19457", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "7675", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "18002", "type": [1, 1, 0, 0], "emotion": 2, "goal": [19, 6, 15, 13]}, {"id": "2527", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 13, 5]}, {"id": "9347", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "8301", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 18]}, {"id": "1422", "type": [1, 1, 0, 0], "emotion": 1, "goal": [15, 7]}, {"id": "7149", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 7]}, {"id": "30", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 4]}, {"id": "1668", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13, 4]}, {"id": "2923", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "16716", "type": [1, 0, 0, 0], "emotion": 6, "goal": [17, 16]}, {"id": "14802", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 3]}, {"id": "1602", "type": [1, 1, 0, 0], "emotion": 4, "goal": [7, 6]}, {"id": "6890", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "8187", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 0]}, {"id": "6064", "type": [1, 0, 0, 0], "emotion": 8, "goal": [13, 1]}, {"id": "11617", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 6, 15]}, {"id": "20096", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6186", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 16]}, {"id": "7398", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 3]}, {"id": "6325", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13]}, {"id": "17730", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 13, 4, 15, 3]}, {"id": "17355", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "6080", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 9]}, {"id": "12055", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "4081", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "19773", "type": [1, 1, 0, 0], "emotion": 0, "goal": [11, 3, 1]}, {"id": "6702", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "15452", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6860", "type": [1, 0, 0, 0], "emotion": 4, "goal": [16]}, {"id": "10407", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 20, 15]}, {"id": "17313", "type": [1, 0, 0, 0], "emotion": 8, "goal": [0, 11]}, {"id": "2811", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 1, 9]}, {"id": "18538", "type": [1, 1, 0, 0], "emotion": 8, "goal": [13, 3]}, {"id": "12404", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 3]}, {"id": "12841", "type": [1, 0, 1, 0], "emotion": 4, "goal": [12, 3]}, {"id": "11924", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 15]}, {"id": "1919", "type": [1, 1, 0, 0], "emotion": 6, "goal": [15, 17]}, {"id": "10831", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13]}, {"id": "3124", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13622", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 15, 10]}, {"id": "14500", "type": [1, 1, 0, 0], "emotion": 4, "goal": [20, 3]}, {"id": "3798", "type": [1, 0, 0, 0], "emotion": 2, "goal": [17, 13, 4]}, {"id": "15531", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "12720", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 12]}, {"id": "18021", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "12746", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12433", "type": [1, 0, 0, 0], "emotion": 2, "goal": [4, 5, 15, 3]}, {"id": "9365", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13745", "type": [1, 0, 1, 1], "emotion": 4, "goal": [4, 13, 3]}, {"id": "15973", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "2938", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "18581", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13]}, {"id": "14999", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9, 3]}, {"id": "15023", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 3, 15]}, {"id": "1921", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 18]}, {"id": "14312", "type": [1, 0, 1, 1], "emotion": 4, "goal": [4, 3]}, {"id": "8706", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "13214", "type": [1, 0, 0, 0], "emotion": 1, "goal": [7, 3]}, {"id": "553", "type": [1, 0, 1, 1], "emotion": 1, "goal": [12, 0, 1]}, {"id": "2689", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3766", "type": [1, 1, 0, 0], "emotion": 4, "goal": [2, 3]}, {"id": "3358", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "8169", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "9923", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15, 2, 14, 9]}, {"id": "5509", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 2, 3]}, {"id": "7900", "type": [1, 1, 0, 0], "emotion": 1, "goal": [8, 13, 3]}, {"id": "18084", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13, 15]}, {"id": "20040", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3988", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "14865", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 3]}, {"id": "9166", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15, 3, 2]}, {"id": "1967", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 12]}, {"id": "3711", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "18393", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "8808", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "18732", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "11871", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 3]}, {"id": "9621", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 2]}, {"id": "13999", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5117", "type": [1, 0, 1, 1], "emotion": 4, "goal": [4, 13]}, {"id": "13753", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "6099", "type": [1, 0, 0, 0], "emotion": 5, "goal": [11, 3]}, {"id": "16330", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17415", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6]}, {"id": "9136", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14003", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 15, 12, 3]}, {"id": "8567", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 5, 3, 10]}, {"id": "11686", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 3]}, {"id": "12873", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 15]}, {"id": "12266", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "12416", "type": [1, 0, 0, 0], "emotion": 4, "goal": [15, 3]}, {"id": "4751", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "17249", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19198", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "8526", "type": [1, 0, 0, 0], "emotion": 4, "goal": [18, 5, 4, 13]}, {"id": "8416", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 15]}, {"id": "19575", "type": [1, 0, 0, 0], "emotion": 1, "goal": [15, 10]}, {"id": "9838", "type": [1, 0, 1, 0], "emotion": 7, "goal": [3, 13]}, {"id": "18348", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5904", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "2416", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 8, 3]}, {"id": "9675", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "12507", "type": [1, 1, 0, 0], "emotion": 4, "goal": [11, 4, 5, 15]}, {"id": "10530", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "11125", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "2962", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "18796", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14454", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 12, 3]}, {"id": "5027", "type": [1, 0, 1, 1], "emotion": 1, "goal": [13, 16]}, {"id": "2534", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "3814", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "5500", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "3142", "type": [1, 0, 1, 0], "emotion": 2, "goal": [4, 5, 6, 15]}, {"id": "8121", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19565", "type": [1, 0, 1, 0], "emotion": 4, "goal": [15, 4, 13, 3]}, {"id": "11914", "type": [1, 0, 1, 0], "emotion": 4, "goal": [15, 3]}, {"id": "4628", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "4007", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "16858", "type": [1, 0, 1, 0], "emotion": 4, "goal": [4, 15, 3]}, {"id": "2405", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "533", "type": [1, 0, 1, 1], "emotion": 1, "goal": [8, 3]}, {"id": "3129", "type": [1, 1, 0, 0], "emotion": 5, "goal": [13, 4, 18, 6, 15, 3]}, {"id": "10524", "type": [1, 1, 0, 0], "emotion": 8, "goal": [12, 3, 11]}, {"id": "16327", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5037", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15, 3]}, {"id": "14840", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 3, 11]}, {"id": "1069", "type": [1, 1, 0, 0], "emotion": 1, "goal": [15, 1]}, {"id": "18098", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13, 6]}, {"id": "3273", "type": [1, 0, 1, 1], "emotion": 4, "goal": [18, 3]}, {"id": "20082", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 7, 3]}, {"id": "7958", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "6042", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 5, 15]}, {"id": "744", "type": [1, 0, 0, 0], "emotion": 0, "goal": [4, 11]}, {"id": "18702", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13]}, {"id": "16644", "type": [1, 1, 0, 0], "emotion": 8, "goal": [0, 11, 1]}, {"id": "811", "type": [1, 1, 0, 0], "emotion": 1, "goal": [8, 13]}, {"id": "16052", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "19284", "type": [1, 1, 0, 0], "emotion": 4, "goal": [9, 3, 13]}, {"id": "11887", "type": [1, 1, 0, 0], "emotion": 8, "goal": [13, 1, 11, 3]}, {"id": "122", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 19]}, {"id": "6594", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 6, 3]}, {"id": "2801", "type": [1, 1, 0, 0], "emotion": 5, "goal": [4, 5, 15]}, {"id": "7623", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "6464", "type": [1, 0, 0, 0], "emotion": 6, "goal": [16, 11, 3]}, {"id": "8801", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "3565", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 16]}, {"id": "10763", "type": [1, 1, 0, 0], "emotion": 9, "goal": [12, 16]}, {"id": "1381", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17218", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "15486", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13]}, {"id": "20156", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 6, 3]}, {"id": "1513", "type": [1, 1, 0, 0], "emotion": 4, "goal": [8, 2]}, {"id": "16676", "type": [1, 0, 1, 0], "emotion": 9, "goal": [3, 16]}, {"id": "13776", "type": [1, 0, 1, 0], "emotion": 0, "goal": [13, 0, 11, 1]}, {"id": "5824", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "18388", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "14426", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 13]}, {"id": "8934", "type": [1, 0, 0, 0], "emotion": 2, "goal": [12, 7]}, {"id": "18750", "type": [1, 0, 1, 1], "emotion": 4, "goal": [0, 3]}, {"id": "6280", "type": [1, 1, 0, 0], "emotion": 0, "goal": [11, 18]}, {"id": "13487", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "12903", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "1889", "type": [1, 0, 0, 0], "emotion": 4, "goal": [1, 13]}, {"id": "12735", "type": [1, 1, 0, 0], "emotion": 4, "goal": [19, 3]}, {"id": "13696", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "16631", "type": [1, 1, 0, 0], "emotion": 5, "goal": [17, 4]}, {"id": "3039", "type": [1, 1, 0, 0], "emotion": 1, "goal": [12, 14]}, {"id": "522", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 9]}, {"id": "7332", "type": [1, 0, 0, 0], "emotion": 4, "goal": [1, 3]}, {"id": "17212", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "11613", "type": [1, 0, 0, 0], "emotion": 1, "goal": [0, 3]}, {"id": "8577", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "3011", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "14915", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 15, 3]}, {"id": "3012", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 20]}, {"id": "12319", "type": [1, 1, 0, 0], "emotion": 5, "goal": [15, 6, 20, 3]}, {"id": "14166", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "16309", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 3, 20]}, {"id": "17727", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3387", "type": [1, 0, 1, 1], "emotion": 4, "goal": [14, 9]}, {"id": "1403", "type": [1, 0, 1, 0], "emotion": 2, "goal": [5, 4, 6, 15]}, {"id": "20140", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 3]}, {"id": "9581", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "2313", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "5756", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 7]}, {"id": "13170", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 3]}, {"id": "15143", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "18222", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 10, 13]}, {"id": "4455", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "15170", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 15, 13, 3]}, {"id": "8733", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "14615", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 10]}, {"id": "13854", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "13356", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "10167", "type": [1, 1, 0, 0], "emotion": 5, "goal": [2, 3]}, {"id": "5855", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "17493", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "17647", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "11649", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3882", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 4, 0]}, {"id": "10040", "type": [1, 0, 0, 0], "emotion": 3, "goal": [1, 0]}]