[{"id": "10188", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 3]}, {"id": "6096", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5638", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "14512", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 20]}, {"id": "1262", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "4067", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "5297", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5425", "type": [1, 0, 0, 0], "emotion": 0, "goal": [13, 12, 11]}, {"id": "15419", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 10]}, {"id": "9021", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "6163", "type": [1, 0, 1, 1], "emotion": 5, "goal": [4, 5]}, {"id": "3732", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 16]}, {"id": "15689", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 3]}, {"id": "1053", "type": [1, 0, 0, 0], "emotion": 2, "goal": [12, 11, 6]}, {"id": "12934", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "7488", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13042", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13]}, {"id": "16402", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 15, 6, 3]}, {"id": "9406", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "15966", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 19, 3]}, {"id": "6643", "type": [1, 0, 1, 0], "emotion": 5, "goal": [13, 6, 15, 3, 17]}, {"id": "1789", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "11131", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 16]}, {"id": "6048", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 11]}, {"id": "12613", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "5319", "type": [1, 0, 1, 0], "emotion": 5, "goal": [4, 6, 15, 3]}, {"id": "8053", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13, 3]}, {"id": "7059", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "17374", "type": [1, 0, 1, 1], "emotion": 4, "goal": [12, 18]}, {"id": "13349", "type": [1, 1, 0, 0], "emotion": 1, "goal": [3, 16]}, {"id": "14902", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13810", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14455", "type": [1, 0, 1, 0], "emotion": 2, "goal": [15, 10, 3]}, {"id": "7460", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6513", "type": [1, 0, 1, 0], "emotion": 7, "goal": [3, 7, 15]}, {"id": "5471", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9]}, {"id": "14178", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 3]}, {"id": "15535", "type": [1, 0, 1, 1], "emotion": 4, "goal": [18, 3]}, {"id": "9559", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "9019", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 11]}, {"id": "2162", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "16972", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "17689", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1628", "type": [1, 0, 1, 1], "emotion": 5, "goal": [17, 4, 5]}, {"id": "3331", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 5, 4, 3]}, {"id": "14399", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 18]}, {"id": "11519", "type": [1, 1, 0, 0], "emotion": 3, "goal": [11, 4, 15]}, {"id": "9879", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 13]}, {"id": "19133", "type": [1, 1, 0, 0], "emotion": 6, "goal": [13, 20]}, {"id": "10337", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "12893", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "10473", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "4163", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "20012", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 9, 1, 3]}, {"id": "5735", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 16]}, {"id": "12895", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "495", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3, 12]}, {"id": "12654", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 10]}, {"id": "16217", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 6, 15, 10, 3]}, {"id": "16035", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 5, 15]}, {"id": "9100", "type": [1, 0, 0, 0], "emotion": 1, "goal": [3, 9, 19, 13]}, {"id": "1051", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 11, 1]}, {"id": "2099", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "376", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 5]}, {"id": "11159", "type": [1, 1, 0, 0], "emotion": 9, "goal": [13, 16]}, {"id": "19969", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "5929", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "10952", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 6]}, {"id": "19878", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13, 15]}, {"id": "17932", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "9810", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "293", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 4]}, {"id": "12298", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "10787", "type": [1, 0, 1, 1], "emotion": 2, "goal": [10, 6, 15]}, {"id": "17286", "type": [1, 1, 0, 0], "emotion": 10, "goal": [3, 13]}, {"id": "17789", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 15, 10, 3]}, {"id": "9337", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3, 2]}, {"id": "19060", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "19656", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "2510", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 5]}, {"id": "5115", "type": [1, 0, 1, 1], "emotion": 5, "goal": [18, 13, 4, 3]}, {"id": "4068", "type": [1, 0, 0, 0], "emotion": 7, "goal": [7, 18, 13, 3, 1]}, {"id": "4387", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 6]}, {"id": "10784", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "2472", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 15, 10, 3]}, {"id": "19043", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "3082", "type": [1, 1, 0, 0], "emotion": 9, "goal": [13, 0, 11, 3, 1]}, {"id": "9428", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 15]}, {"id": "17262", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "6243", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 12, 4]}, {"id": "17322", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "12701", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "18076", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15]}, {"id": "1486", "type": [1, 0, 0, 0], "emotion": 5, "goal": [15]}, {"id": "10590", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5769", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "6699", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "11475", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "8537", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 18, 3]}, {"id": "16519", "type": [1, 1, 0, 0], "emotion": 7, "goal": [7, 17]}, {"id": "10691", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "5535", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "8856", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "9605", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "3990", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 13]}, {"id": "6015", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 12]}, {"id": "15813", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "13627", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 13]}, {"id": "11377", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "13182", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 6]}, {"id": "17693", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4, 19]}, {"id": "4774", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 2]}, {"id": "1265", "type": [1, 1, 0, 0], "emotion": 0, "goal": [11, 0]}, {"id": "16168", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "10991", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4]}, {"id": "19288", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3496", "type": [1, 1, 1, 0], "emotion": 2, "goal": [13, 20, 4]}, {"id": "6426", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 20]}, {"id": "7189", "type": [1, 1, 0, 0], "emotion": 4, "goal": [10, 3]}, {"id": "11746", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 4, 3]}, {"id": "16961", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "7101", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3570", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 10, 3]}, {"id": "5589", "type": [1, 0, 1, 0], "emotion": 4, "goal": [16]}, {"id": "9109", "type": [1, 1, 0, 0], "emotion": 4, "goal": [5, 15]}, {"id": "3968", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "463", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "19277", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 6, 3]}, {"id": "6962", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 0]}, {"id": "954", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 4]}, {"id": "13740", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12, 3]}, {"id": "8549", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "19799", "type": [1, 0, 1, 0], "emotion": 9, "goal": [13, 16]}, {"id": "19990", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "5423", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4]}, {"id": "15722", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15, 3]}, {"id": "6689", "type": [1, 0, 0, 0], "emotion": 4, "goal": [8, 14, 9, 3]}, {"id": "228", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 3]}, {"id": "9403", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 2, 3]}, {"id": "18181", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16919", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "18033", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "9082", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 15]}, {"id": "18031", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 6, 15]}, {"id": "6805", "type": [1, 1, 0, 0], "emotion": 1, "goal": [5, 12]}, {"id": "18237", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "6985", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3]}, {"id": "18838", "type": [1, 0, 0, 0], "emotion": 5, "goal": [18, 3]}, {"id": "14551", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9767", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 20]}, {"id": "19470", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "1538", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 13, 4]}, {"id": "14846", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "12260", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 12]}, {"id": "8551", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9971", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 15, 13]}, {"id": "17065", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "13192", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "1514", "type": [1, 0, 0, 0], "emotion": 4, "goal": [4, 13]}, {"id": "9386", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "7906", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 5, 15]}, {"id": "5688", "type": [1, 1, 0, 0], "emotion": 0, "goal": [3, 0, 11]}, {"id": "1557", "type": [1, 1, 0, 0], "emotion": 4, "goal": [10, 4]}, {"id": "19681", "type": [1, 1, 0, 0], "emotion": 6, "goal": [6, 3]}, {"id": "6765", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 4, 3]}, {"id": "7871", "type": [1, 0, 1, 0], "emotion": 2, "goal": [15, 10]}, {"id": "16295", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 5, 14, 3]}, {"id": "16582", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "7108", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 16]}, {"id": "19714", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "19232", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "11864", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15, 3]}, {"id": "7026", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "11625", "type": [1, 0, 1, 1], "emotion": 4, "goal": [18, 13, 3]}, {"id": "6938", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17883", "type": [1, 1, 1, 1], "emotion": 4, "goal": [4, 3]}, {"id": "4228", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "16434", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 15, 10, 3]}, {"id": "5673", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4]}, {"id": "15749", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "14602", "type": [1, 1, 0, 0], "emotion": 0, "goal": [12, 0]}, {"id": "6477", "type": [1, 0, 1, 0], "emotion": 0, "goal": [11, 1]}, {"id": "15215", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "16025", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13]}, {"id": "2636", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "7125", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "10880", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 19]}, {"id": "5859", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "16443", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "15065", "type": [1, 0, 0, 0], "emotion": 4, "goal": [9, 16]}, {"id": "4826", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "14912", "type": [1, 1, 0, 0], "emotion": 4, "goal": [10, 3]}, {"id": "19104", "type": [1, 0, 0, 0], "emotion": 7, "goal": [7, 11]}, {"id": "5445", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "7702", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 16]}, {"id": "16059", "type": [1, 1, 1, 1], "emotion": 2, "goal": [13, 17, 3, 10]}, {"id": "14684", "type": [1, 0, 0, 0], "emotion": 2, "goal": [12, 11]}, {"id": "2347", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 20]}, {"id": "17751", "type": [1, 0, 0, 0], "emotion": 6, "goal": [15, 2, 3]}, {"id": "370", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 20, 4]}, {"id": "16041", "type": [1, 0, 1, 1], "emotion": 4, "goal": [10, 3]}, {"id": "16928", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "10644", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 10]}, {"id": "3319", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 4]}, {"id": "588", "type": [1, 1, 0, 0], "emotion": 9, "goal": [13, 3]}, {"id": "8697", "type": [1, 0, 1, 0], "emotion": 6, "goal": [3, 17]}, {"id": "6035", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 20]}, {"id": "18578", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14913", "type": [1, 1, 0, 0], "emotion": 4, "goal": [11, 0, 3]}, {"id": "3848", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 11]}, {"id": "8843", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 16]}, {"id": "9162", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 13, 15, 3]}, {"id": "6114", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "2160", "type": [1, 1, 0, 0], "emotion": 4, "goal": [1, 16]}, {"id": "9666", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 16]}, {"id": "10786", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "3873", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 16]}, {"id": "4736", "type": [1, 1, 0, 0], "emotion": 8, "goal": [11, 3]}, {"id": "18052", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "2470", "type": [1, 0, 0, 0], "emotion": 0, "goal": [12, 0, 11, 1]}, {"id": "10176", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "15237", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "2028", "type": [1, 1, 0, 0], "emotion": 2, "goal": [14, 3, 15, 10]}, {"id": "7435", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 10, 15]}, {"id": "12134", "type": [1, 0, 1, 0], "emotion": 2, "goal": [3, 10]}, {"id": "4161", "type": [1, 0, 1, 1], "emotion": 4, "goal": [4, 15, 2]}, {"id": "5069", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 3, 6, 15]}, {"id": "12927", "type": [1, 0, 0, 0], "emotion": 2, "goal": [12, 3]}, {"id": "9510", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 8]}, {"id": "19735", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 10]}, {"id": "17792", "type": [1, 0, 1, 1], "emotion": 4, "goal": [0, 3, 13]}, {"id": "870", "type": [1, 0, 1, 0], "emotion": 4, "goal": [12, 19]}, {"id": "12618", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 6, 15]}, {"id": "16724", "type": [1, 0, 1, 1], "emotion": 2, "goal": [4, 5]}, {"id": "11342", "type": [1, 0, 1, 0], "emotion": 4, "goal": [12, 3]}, {"id": "19118", "type": [1, 0, 1, 1], "emotion": 2, "goal": [4, 3]}, {"id": "15352", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "13800", "type": [1, 0, 1, 1], "emotion": 7, "goal": [13, 3]}, {"id": "5733", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 16]}, {"id": "9278", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 12, 3, 20]}, {"id": "1036", "type": [1, 1, 0, 0], "emotion": 4, "goal": [5, 15]}, {"id": "18425", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "10047", "type": [1, 0, 0, 0], "emotion": 2, "goal": [15, 6]}, {"id": "11044", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 2]}, {"id": "16855", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "285", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 6]}, {"id": "8680", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "5690", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "9596", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 12, 9]}, {"id": "1340", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "6246", "type": [1, 1, 0, 0], "emotion": 6, "goal": [13, 2, 15]}, {"id": "2118", "type": [1, 0, 1, 1], "emotion": 4, "goal": [17, 13, 3]}, {"id": "2301", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3, 2]}, {"id": "16894", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3280", "type": [1, 1, 0, 0], "emotion": 1, "goal": [3, 16]}, {"id": "14135", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "9291", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "13446", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "3769", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "20122", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 13]}, {"id": "14264", "type": [1, 1, 0, 0], "emotion": 2, "goal": [18, 3]}, {"id": "19976", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 3]}, {"id": "14366", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "3152", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3840", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4]}, {"id": "7853", "type": [1, 1, 0, 0], "emotion": 4, "goal": [5, 16]}, {"id": "7228", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 5, 15, 12]}, {"id": "7704", "type": [1, 0, 0, 0], "emotion": 7, "goal": [7, 16]}, {"id": "179", "type": [1, 1, 0, 0], "emotion": 2, "goal": [18, 4, 15]}, {"id": "11984", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "19028", "type": [1, 1, 0, 0], "emotion": 0, "goal": [11, 4]}, {"id": "12406", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17616", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "18198", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 16]}, {"id": "19116", "type": [1, 1, 0, 0], "emotion": 7, "goal": [7, 3]}, {"id": "15784", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14507", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16034", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "19226", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "1827", "type": [1, 0, 1, 1], "emotion": 8, "goal": [18, 4, 11]}, {"id": "18056", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 16]}, {"id": "1485", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 12]}, {"id": "6753", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 3]}, {"id": "12492", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4154", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15, 12]}, {"id": "9680", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "17307", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 12, 16]}, {"id": "12742", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "4368", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 3]}, {"id": "12160", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "6069", "type": [1, 1, 0, 0], "emotion": 1, "goal": [19, 13]}, {"id": "75", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "15253", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "15021", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "6435", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 16]}, {"id": "9012", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9, 3]}, {"id": "3962", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 3]}, {"id": "19349", "type": [1, 0, 1, 0], "emotion": 0, "goal": [17, 13, 11]}, {"id": "20128", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15, 5]}, {"id": "3385", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 5, 15]}, {"id": "10230", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 3]}, {"id": "19342", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 3]}, {"id": "11072", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "14907", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 6]}, {"id": "7935", "type": [1, 0, 1, 1], "emotion": 4, "goal": [8, 3]}, {"id": "5481", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "19518", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 3, 10]}, {"id": "18594", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "2420", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 4, 5, 6]}, {"id": "18317", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13322", "type": [1, 0, 0, 0], "emotion": 6, "goal": [17, 20]}, {"id": "11450", "type": [1, 1, 0, 0], "emotion": 4, "goal": [7, 2, 3]}, {"id": "214", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 17]}, {"id": "17975", "type": [1, 1, 0, 0], "emotion": 4, "goal": [16]}, {"id": "19638", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15, 3]}, {"id": "7829", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 15, 11]}, {"id": "2015", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "19696", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "11134", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 15]}, {"id": "14922", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16874", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 6]}, {"id": "18525", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "54", "type": [1, 1, 0, 0], "emotion": 7, "goal": [16]}, {"id": "7848", "type": [1, 0, 1, 1], "emotion": 5, "goal": [18, 15, 20]}, {"id": "4874", "type": [1, 0, 1, 0], "emotion": 3, "goal": [16]}, {"id": "13024", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14785", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 6, 15]}, {"id": "14993", "type": [1, 1, 0, 0], "emotion": 4, "goal": [16]}, {"id": "14336", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "12597", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "11485", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 3]}, {"id": "20104", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 3]}, {"id": "11983", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9348", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 5, 6, 15]}, {"id": "3215", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5233", "type": [1, 1, 0, 0], "emotion": 2, "goal": [18, 13, 15, 3, 10]}, {"id": "3940", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6]}, {"id": "772", "type": [1, 0, 0, 0], "emotion": 1, "goal": [20, 3]}, {"id": "13383", "type": [1, 1, 0, 0], "emotion": 4, "goal": [14, 13]}, {"id": "10554", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "239", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9]}, {"id": "1793", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13, 6]}, {"id": "10996", "type": [1, 0, 0, 0], "emotion": 0, "goal": [3, 11]}, {"id": "12081", "type": [1, 0, 0, 0], "emotion": 5, "goal": [17, 18, 3]}, {"id": "15506", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 16]}, {"id": "5796", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "19121", "type": [1, 0, 1, 0], "emotion": 4, "goal": [18, 3]}, {"id": "15423", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 2]}, {"id": "5841", "type": [1, 0, 1, 0], "emotion": 2, "goal": [15, 6]}, {"id": "14539", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "9061", "type": [1, 1, 0, 0], "emotion": 2, "goal": [7, 15, 3]}, {"id": "19742", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 15]}, {"id": "6982", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "8720", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "1135", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "6863", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "2647", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13986", "type": [1, 0, 0, 0], "emotion": 2, "goal": [3, 20]}, {"id": "17620", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "19790", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "15529", "type": [1, 1, 0, 0], "emotion": 4, "goal": [11, 9]}, {"id": "15557", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "2340", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 16]}, {"id": "9298", "type": [1, 0, 0, 0], "emotion": 0, "goal": [19, 12, 1]}, {"id": "2251", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 16]}, {"id": "6507", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 11, 1]}, {"id": "17583", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4932", "type": [1, 1, 0, 0], "emotion": 1, "goal": [4, 13]}, {"id": "3811", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "15983", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 0, 3]}, {"id": "5926", "type": [1, 1, 0, 0], "emotion": 4, "goal": [8, 12]}, {"id": "16244", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19570", "type": [1, 1, 0, 0], "emotion": 4, "goal": [5, 3]}, {"id": "4172", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 15, 3]}, {"id": "2047", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12031", "type": [1, 1, 0, 0], "emotion": 7, "goal": [0, 7]}, {"id": "9661", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "12253", "type": [1, 0, 1, 1], "emotion": 0, "goal": [13, 4]}, {"id": "17418", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "8023", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "20083", "type": [1, 0, 0, 0], "emotion": 8, "goal": [11, 0]}, {"id": "151", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13]}, {"id": "4898", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "15771", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3, 12, 0]}, {"id": "16445", "type": [1, 0, 0, 0], "emotion": 2, "goal": [4, 15, 3]}, {"id": "11127", "type": [1, 0, 1, 0], "emotion": 4, "goal": [12, 16]}, {"id": "5329", "type": [1, 1, 0, 0], "emotion": 1, "goal": [3]}, {"id": "5366", "type": [1, 0, 1, 0], "emotion": 4, "goal": [4, 3]}, {"id": "12352", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "14916", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 10, 3]}, {"id": "7621", "type": [1, 0, 1, 1], "emotion": 4, "goal": [5, 3]}, {"id": "5494", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15, 3]}, {"id": "11738", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "5816", "type": [1, 0, 0, 0], "emotion": 4, "goal": [4, 3]}, {"id": "7003", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "9563", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "14326", "type": [1, 0, 0, 0], "emotion": 0, "goal": [3, 11]}, {"id": "18807", "type": [1, 0, 1, 1], "emotion": 4, "goal": [12, 13]}, {"id": "12126", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6383", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 11, 1]}, {"id": "14783", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 3]}, {"id": "18531", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3395", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "16148", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "140", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "7064", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 13]}, {"id": "18316", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "4976", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 5, 15, 6, 3]}, {"id": "10415", "type": [1, 1, 0, 0], "emotion": 8, "goal": [13, 3]}, {"id": "5680", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "8466", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 13]}, {"id": "11576", "type": [1, 1, 0, 0], "emotion": 8, "goal": [5, 0, 1]}, {"id": "18123", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14536", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13]}, {"id": "6458", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "9882", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "6611", "type": [1, 1, 0, 0], "emotion": 3, "goal": [0, 11, 1]}, {"id": "6339", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "10491", "type": [1, 1, 0, 0], "emotion": 4, "goal": [14, 13, 3]}, {"id": "7193", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6]}, {"id": "17806", "type": [1, 0, 1, 0], "emotion": 2, "goal": [5, 15, 3, 13]}, {"id": "6738", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9, 3]}, {"id": "1214", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3, 4]}, {"id": "5869", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13]}, {"id": "4703", "type": [1, 1, 0, 0], "emotion": 7, "goal": [4, 3]}, {"id": "2480", "type": [1, 1, 0, 0], "emotion": 6, "goal": [13, 15, 17, 3]}, {"id": "64", "type": [1, 0, 1, 0], "emotion": 2, "goal": [4, 10]}, {"id": "12509", "type": [1, 1, 0, 0], "emotion": 1, "goal": [4, 5, 15]}, {"id": "6461", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "5534", "type": [1, 0, 1, 0], "emotion": 4, "goal": [12, 3]}, {"id": "17205", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "12216", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 16]}, {"id": "5700", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "19022", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "6142", "type": [1, 1, 0, 0], "emotion": 8, "goal": [11, 12, 1]}, {"id": "3474", "type": [1, 0, 1, 0], "emotion": 0, "goal": [13, 0, 1]}, {"id": "19215", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16690", "type": [1, 0, 1, 0], "emotion": 2, "goal": [3, 16]}, {"id": "936", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 8]}, {"id": "12783", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "7650", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "8774", "type": [1, 0, 1, 0], "emotion": 4, "goal": [11, 4]}, {"id": "16264", "type": [1, 0, 1, 1], "emotion": 2, "goal": [15, 4, 13, 3]}, {"id": "17075", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15, 20]}, {"id": "15418", "type": [1, 0, 1, 0], "emotion": 1, "goal": [4, 16]}, {"id": "3892", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 13]}, {"id": "251", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 1]}, {"id": "17910", "type": [1, 0, 0, 0], "emotion": 8, "goal": [1, 3, 0]}, {"id": "12571", "type": [1, 0, 0, 0], "emotion": 4, "goal": [11, 0]}, {"id": "11563", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 6]}, {"id": "2445", "type": [1, 0, 0, 0], "emotion": 5, "goal": [18, 6, 20]}, {"id": "3098", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "18935", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "17558", "type": [1, 1, 0, 0], "emotion": 2, "goal": [10, 6]}, {"id": "8818", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "2730", "type": [1, 0, 1, 0], "emotion": 1, "goal": [7, 4]}, {"id": "10134", "type": [1, 1, 0, 0], "emotion": 0, "goal": [13, 0, 11, 1, 3]}, {"id": "12368", "type": [1, 0, 0, 0], "emotion": 4, "goal": [1, 11, 0, 12]}, {"id": "15861", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 3]}, {"id": "13982", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "9032", "type": [1, 0, 1, 0], "emotion": 1, "goal": [16]}, {"id": "12355", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 9, 13]}, {"id": "10671", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "18247", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "6754", "type": [1, 1, 0, 0], "emotion": 4, "goal": [10, 13, 9]}, {"id": "16824", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "7356", "type": [1, 0, 0, 0], "emotion": 9, "goal": [15, 12]}, {"id": "18961", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "13882", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9, 3]}, {"id": "507", "type": [1, 0, 0, 0], "emotion": 2, "goal": [5, 4]}, {"id": "15731", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "8762", "type": [1, 1, 0, 0], "emotion": 4, "goal": [11, 12]}, {"id": "4445", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5778", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "18168", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "4021", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 6, 15]}, {"id": "13648", "type": [1, 1, 0, 0], "emotion": 7, "goal": [13, 7]}, {"id": "19717", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15, 3]}, {"id": "17547", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 3]}, {"id": "315", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 2]}, {"id": "13072", "type": [1, 1, 0, 0], "emotion": 7, "goal": [0, 16]}, {"id": "5569", "type": [1, 0, 1, 1], "emotion": 4, "goal": [3, 10, 4]}, {"id": "5933", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "3359", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 12]}, {"id": "18153", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15]}, {"id": "9904", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "4609", "type": [1, 1, 0, 0], "emotion": 6, "goal": [13, 17]}, {"id": "6746", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "18819", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17096", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13, 15]}, {"id": "7872", "type": [1, 1, 0, 0], "emotion": 5, "goal": [13, 4, 5, 6, 15, 10]}, {"id": "11545", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14629", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 15]}, {"id": "11404", "type": [1, 0, 0, 0], "emotion": 1, "goal": [3, 12]}, {"id": "3369", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 11]}, {"id": "20061", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 16]}, {"id": "17334", "type": [1, 1, 0, 0], "emotion": 0, "goal": [11, 3]}, {"id": "12984", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 13, 3]}, {"id": "2632", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "7188", "type": [1, 0, 0, 0], "emotion": 4, "goal": [6, 3]}, {"id": "11531", "type": [1, 0, 1, 1], "emotion": 4, "goal": [4, 5, 15]}, {"id": "647", "type": [1, 0, 0, 0], "emotion": 0, "goal": [1, 11]}, {"id": "14655", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 20]}, {"id": "13174", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "4810", "type": [1, 0, 0, 0], "emotion": 1, "goal": [13, 0]}, {"id": "19458", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12, 16]}, {"id": "5025", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "18618", "type": [1, 0, 1, 0], "emotion": 4, "goal": [4, 13, 3]}, {"id": "13769", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 15]}, {"id": "638", "type": [1, 0, 1, 0], "emotion": 1, "goal": [8, 3]}, {"id": "16451", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5320", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 3]}, {"id": "6633", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 9, 3]}, {"id": "324", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "18054", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 19, 3]}, {"id": "12992", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "18004", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "7496", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 20]}, {"id": "19900", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 10, 6, 15]}, {"id": "5919", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 3, 6]}, {"id": "7374", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "1296", "type": [1, 1, 0, 0], "emotion": 8, "goal": [0, 1]}, {"id": "1663", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12, 11]}, {"id": "11815", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "1516", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 5]}, {"id": "17265", "type": [1, 0, 1, 0], "emotion": 4, "goal": [12, 16]}, {"id": "6736", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13]}, {"id": "8042", "type": [1, 1, 0, 0], "emotion": 4, "goal": [14, 9, 3]}, {"id": "942", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 1, 8]}, {"id": "9490", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 13, 6]}, {"id": "4833", "type": [1, 0, 1, 0], "emotion": 5, "goal": [5, 6, 15, 4]}, {"id": "15546", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "2762", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "899", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 4, 5]}, {"id": "9993", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17889", "type": [1, 0, 0, 0], "emotion": 4, "goal": [0, 11, 3]}, {"id": "11741", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "3586", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 11]}, {"id": "7518", "type": [1, 0, 0, 0], "emotion": 6, "goal": [13, 17]}, {"id": "15764", "type": [1, 0, 1, 1], "emotion": 0, "goal": [0, 11, 3]}, {"id": "13602", "type": [1, 0, 0, 0], "emotion": 6, "goal": [13, 3, 17]}, {"id": "11060", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 13]}, {"id": "16060", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 3]}, {"id": "625", "type": [1, 1, 0, 0], "emotion": 2, "goal": [8, 15]}, {"id": "7561", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "9909", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 6, 15]}, {"id": "16621", "type": [1, 0, 0, 0], "emotion": 0, "goal": [11, 0]}, {"id": "12169", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 17]}, {"id": "5322", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 3]}, {"id": "13034", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 0]}, {"id": "10372", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "17157", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6]}, {"id": "4121", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 15]}, {"id": "11711", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9216", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "8886", "type": [1, 0, 1, 0], "emotion": 4, "goal": [4, 16]}, {"id": "18138", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "15334", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "1155", "type": [1, 1, 0, 0], "emotion": 1, "goal": [12, 0]}, {"id": "15485", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "2519", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "3552", "type": [1, 0, 1, 0], "emotion": 2, "goal": [5, 6, 15]}, {"id": "10235", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 15, 3, 20]}, {"id": "12246", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "8498", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "15383", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "17031", "type": [1, 1, 0, 0], "emotion": 4, "goal": [9, 16]}, {"id": "4597", "type": [1, 1, 0, 0], "emotion": 8, "goal": [0, 13]}, {"id": "19244", "type": [1, 0, 1, 1], "emotion": 4, "goal": [15, 3]}, {"id": "13131", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "18061", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6797", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4, 5, 3]}, {"id": "18753", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "9444", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 12, 15]}, {"id": "17332", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "14498", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "14973", "type": [1, 0, 1, 0], "emotion": 4, "goal": [16, 4]}, {"id": "11721", "type": [1, 0, 0, 0], "emotion": 7, "goal": [3, 16]}, {"id": "2570", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 10]}, {"id": "10423", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "5190", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "16010", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "1323", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 17, 15]}, {"id": "4266", "type": [1, 1, 0, 0], "emotion": 8, "goal": [3, 16]}, {"id": "9841", "type": [1, 0, 0, 0], "emotion": 4, "goal": [10, 16]}, {"id": "18469", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 4]}, {"id": "8924", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 3]}, {"id": "19853", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9977", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 13, 4, 5]}, {"id": "5215", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 15, 10]}, {"id": "10365", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 4, 15, 20]}, {"id": "15976", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "2552", "type": [1, 1, 0, 0], "emotion": 2, "goal": [8, 6, 15, 2]}, {"id": "2817", "type": [1, 0, 0, 0], "emotion": 1, "goal": [3, 16]}, {"id": "19759", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15, 3]}, {"id": "6345", "type": [1, 1, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "15711", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "18490", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12236", "type": [1, 1, 0, 0], "emotion": 7, "goal": [3, 7]}, {"id": "9881", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 6, 3]}, {"id": "9764", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 18]}, {"id": "4775", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 16]}, {"id": "18627", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5738", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3, 20]}, {"id": "16045", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "6849", "type": [1, 0, 1, 0], "emotion": 5, "goal": [4, 6, 15, 10]}, {"id": "9379", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 11]}, {"id": "9413", "type": [1, 0, 0, 0], "emotion": 7, "goal": [6, 11]}, {"id": "17404", "type": [1, 0, 0, 0], "emotion": 0, "goal": [13, 0]}, {"id": "3761", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "9051", "type": [1, 1, 0, 0], "emotion": 3, "goal": [0, 1]}, {"id": "4441", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 2]}, {"id": "17668", "type": [1, 0, 1, 1], "emotion": 2, "goal": [4, 6]}, {"id": "15276", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3247", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 6, 15]}, {"id": "10927", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "14292", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "11142", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "11275", "type": [1, 0, 1, 0], "emotion": 4, "goal": [18, 3]}, {"id": "18058", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13]}, {"id": "12655", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "10930", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 0]}, {"id": "8814", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "15607", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "19476", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "11331", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 11]}, {"id": "11126", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 3, 13]}, {"id": "10878", "type": [1, 0, 0, 0], "emotion": 0, "goal": [12, 0, 11]}, {"id": "7843", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1768", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 2]}, {"id": "2295", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 16]}, {"id": "990", "type": [1, 0, 0, 0], "emotion": 1, "goal": [12]}, {"id": "13193", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 1]}, {"id": "9491", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "9624", "type": [1, 0, 0, 0], "emotion": 4, "goal": [20, 16]}, {"id": "19861", "type": [1, 0, 0, 0], "emotion": 4, "goal": [11, 13]}, {"id": "3113", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 5, 15]}, {"id": "17344", "type": [1, 0, 1, 0], "emotion": 2, "goal": [18, 13]}, {"id": "1915", "type": [1, 0, 0, 0], "emotion": 4, "goal": [6, 15]}, {"id": "18410", "type": [1, 0, 1, 0], "emotion": 4, "goal": [12, 16]}, {"id": "12173", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 11]}, {"id": "15125", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 15, 3]}, {"id": "18472", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14167", "type": [1, 0, 1, 0], "emotion": 5, "goal": [3, 16]}, {"id": "14332", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 12]}, {"id": "14683", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "2246", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 3]}, {"id": "12669", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 6]}, {"id": "17309", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "10374", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "401", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 5]}, {"id": "10768", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "2384", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "19745", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "4328", "type": [1, 1, 0, 0], "emotion": 2, "goal": [10, 16]}, {"id": "18917", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "11015", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3383", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 15]}, {"id": "797", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 3]}, {"id": "1728", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "825", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 13]}, {"id": "13568", "type": [1, 1, 0, 0], "emotion": 7, "goal": [15, 3, 1]}, {"id": "18644", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "8593", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 6, 15]}, {"id": "19631", "type": [1, 1, 0, 0], "emotion": 7, "goal": [7, 3]}, {"id": "18785", "type": [1, 0, 1, 0], "emotion": 4, "goal": [12, 16]}, {"id": "4408", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15, 5]}, {"id": "8628", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "7179", "type": [1, 0, 0, 0], "emotion": 7, "goal": [13, 3]}, {"id": "13146", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "4417", "type": [1, 1, 0, 0], "emotion": 2, "goal": [11, 16]}, {"id": "4771", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 19]}, {"id": "10071", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16075", "type": [1, 0, 1, 0], "emotion": 2, "goal": [8, 10, 3]}, {"id": "11169", "type": [1, 1, 0, 0], "emotion": 4, "goal": [8, 3]}, {"id": "934", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 11]}, {"id": "15069", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 16]}, {"id": "9765", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "11259", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "13151", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "10913", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1917", "type": [1, 0, 1, 0], "emotion": 6, "goal": [17, 4]}, {"id": "9889", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9326", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "5980", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "424", "type": [1, 0, 0, 0], "emotion": 4, "goal": [6, 5]}, {"id": "20055", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 16]}, {"id": "13978", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "1315", "type": [1, 1, 0, 0], "emotion": 6, "goal": [10, 17, 9]}, {"id": "13159", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13]}, {"id": "15335", "type": [1, 0, 1, 0], "emotion": 2, "goal": [4, 6]}, {"id": "9484", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "13640", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "15755", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4]}, {"id": "9644", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "350", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "9802", "type": [1, 0, 0, 0], "emotion": 2, "goal": [4, 10]}, {"id": "11346", "type": [1, 0, 1, 1], "emotion": 4, "goal": [12, 16]}, {"id": "18405", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "15905", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "2901", "type": [1, 0, 0, 0], "emotion": 0, "goal": [1, 11, 0]}, {"id": "12251", "type": [1, 0, 1, 0], "emotion": 7, "goal": [16, 7]}, {"id": "17653", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "9469", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "528", "type": [1, 0, 1, 0], "emotion": 1, "goal": [13, 11, 10]}, {"id": "9551", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9907", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 15, 3]}, {"id": "13497", "type": [1, 0, 0, 0], "emotion": 7, "goal": [3, 7]}, {"id": "1286", "type": [1, 0, 0, 0], "emotion": 4, "goal": [4, 10]}, {"id": "1045", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "18353", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16966", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "6857", "type": [1, 0, 1, 0], "emotion": 1, "goal": [3, 16]}, {"id": "4074", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "20068", "type": [1, 0, 1, 1], "emotion": 4, "goal": [18, 16]}, {"id": "15456", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "3252", "type": [1, 0, 1, 0], "emotion": 1, "goal": [13, 4, 15]}, {"id": "6688", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 3]}, {"id": "17978", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 6, 15]}, {"id": "12736", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "10187", "type": [1, 0, 0, 0], "emotion": 1, "goal": [3, 16]}, {"id": "896", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 5, 15]}, {"id": "3489", "type": [1, 0, 0, 0], "emotion": 0, "goal": [13, 12, 0]}, {"id": "14557", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "18325", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "14923", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13411", "type": [1, 1, 0, 0], "emotion": 4, "goal": [14, 3, 2]}, {"id": "16292", "type": [1, 1, 0, 0], "emotion": 4, "goal": [16]}, {"id": "11692", "type": [1, 1, 0, 0], "emotion": 1, "goal": [5, 15]}, {"id": "14243", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 2]}, {"id": "13265", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "3248", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 5, 15, 6, 3, 10]}, {"id": "6032", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "2019", "type": [1, 1, 0, 0], "emotion": 7, "goal": [13, 18, 7, 3]}, {"id": "2215", "type": [1, 1, 0, 0], "emotion": 2, "goal": [10, 16]}, {"id": "8583", "type": [1, 0, 1, 0], "emotion": 2, "goal": [7, 15, 10, 20]}, {"id": "3380", "type": [1, 0, 0, 0], "emotion": 4, "goal": [18, 13, 20]}, {"id": "3948", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 4]}, {"id": "16156", "type": [1, 0, 1, 1], "emotion": 4, "goal": [3, 16]}, {"id": "14893", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 3]}, {"id": "16755", "type": [1, 1, 0, 0], "emotion": 8, "goal": [12, 3, 0]}, {"id": "10510", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "20021", "type": [1, 1, 0, 0], "emotion": 4, "goal": [5, 13, 3, 9]}, {"id": "9734", "type": [1, 0, 1, 0], "emotion": 2, "goal": [11, 16]}, {"id": "903", "type": [1, 0, 0, 0], "emotion": 7, "goal": [3, 16]}, {"id": "9511", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 5]}, {"id": "11165", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "9826", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "4357", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "10315", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13]}, {"id": "3779", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5184", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 5]}, {"id": "3318", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "945", "type": [1, 1, 0, 0], "emotion": 7, "goal": [6, 10]}, {"id": "8806", "type": [1, 0, 1, 1], "emotion": 2, "goal": [15, 6, 10, 3]}, {"id": "12603", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "9975", "type": [1, 1, 0, 0], "emotion": 5, "goal": [6, 15, 3]}, {"id": "17831", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12, 3]}, {"id": "9984", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 13, 15, 5]}, {"id": "2927", "type": [1, 0, 1, 0], "emotion": 6, "goal": [20, 4]}, {"id": "6349", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "13784", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 12]}, {"id": "4025", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "4044", "type": [1, 1, 0, 0], "emotion": 0, "goal": [13, 3, 1]}, {"id": "16533", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 12, 15]}, {"id": "9110", "type": [1, 0, 0, 0], "emotion": 7, "goal": [3, 7]}, {"id": "17670", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "7034", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "18186", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "19669", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "17810", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 13, 15, 20]}, {"id": "7674", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "1880", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "1338", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13]}, {"id": "2049", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 15]}, {"id": "6639", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13603", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 4, 3]}, {"id": "17214", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 6, 15]}, {"id": "4083", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19646", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 12]}, {"id": "13578", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 9]}, {"id": "18639", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "18927", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "340", "type": [1, 0, 0, 0], "emotion": 8, "goal": [10, 12, 0, 11]}, {"id": "3258", "type": [1, 0, 1, 0], "emotion": 9, "goal": [4, 12]}, {"id": "19760", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1129", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "18763", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 6]}, {"id": "13833", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 6, 3]}, {"id": "11811", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "11857", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 16]}, {"id": "217", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 14]}, {"id": "3483", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 10]}, {"id": "3670", "type": [1, 1, 0, 0], "emotion": 4, "goal": [16]}, {"id": "1047", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "6158", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9]}, {"id": "9247", "type": [1, 0, 0, 0], "emotion": 4, "goal": [0, 11]}, {"id": "14113", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "4821", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 2, 15, 3]}, {"id": "15429", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 10, 3]}, {"id": "1647", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 15]}, {"id": "13039", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "9079", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19692", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "6226", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6843", "type": [1, 0, 1, 0], "emotion": 0, "goal": [0, 3]}, {"id": "1997", "type": [1, 0, 0, 0], "emotion": 0, "goal": [12, 11, 1]}, {"id": "14859", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14679", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 11]}, {"id": "12048", "type": [1, 0, 0, 0], "emotion": 8, "goal": [4, 0]}, {"id": "3400", "type": [1, 1, 0, 0], "emotion": 5, "goal": [13, 6, 15, 10]}, {"id": "12333", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 2, 3]}, {"id": "6574", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "755", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 10]}, {"id": "17694", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 6]}, {"id": "11812", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "8837", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 6, 15]}, {"id": "2463", "type": [1, 0, 1, 0], "emotion": 1, "goal": [8, 3]}, {"id": "7421", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5611", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 4]}, {"id": "11636", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 11, 1]}, {"id": "669", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 13]}, {"id": "15833", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 13]}, {"id": "1471", "type": [1, 1, 0, 0], "emotion": 2, "goal": [10, 15]}, {"id": "7880", "type": [1, 0, 1, 0], "emotion": 1, "goal": [18, 3]}, {"id": "339", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 10]}, {"id": "158", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 15]}, {"id": "9665", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "600", "type": [1, 1, 0, 0], "emotion": 8, "goal": [5, 11, 0]}, {"id": "19806", "type": [1, 0, 1, 0], "emotion": 4, "goal": [11, 13]}, {"id": "3749", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 2]}, {"id": "2274", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 16]}, {"id": "13314", "type": [1, 0, 1, 1], "emotion": 1, "goal": [4, 3]}, {"id": "5252", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "12825", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1329", "type": [1, 1, 0, 0], "emotion": 5, "goal": [18, 13, 17]}, {"id": "9238", "type": [1, 0, 1, 1], "emotion": 5, "goal": [4, 15]}, {"id": "6025", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "8303", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "195", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 10]}, {"id": "7548", "type": [1, 0, 0, 0], "emotion": 8, "goal": [11, 0, 20]}, {"id": "17581", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "15524", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6405", "type": [1, 1, 0, 0], "emotion": 8, "goal": [3, 11]}, {"id": "17704", "type": [1, 0, 0, 0], "emotion": 5, "goal": [15, 10]}, {"id": "13711", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "583", "type": [1, 0, 1, 1], "emotion": 1, "goal": [7, 4, 5]}, {"id": "1754", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 16]}, {"id": "7782", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6203", "type": [1, 0, 0, 0], "emotion": 0, "goal": [1, 0, 3]}, {"id": "10718", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "4360", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 10, 15]}, {"id": "4797", "type": [1, 0, 0, 0], "emotion": 6, "goal": [3, 20]}, {"id": "15349", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "3643", "type": [1, 0, 1, 1], "emotion": 4, "goal": [4, 3]}, {"id": "19509", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 6, 15, 5]}, {"id": "3054", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 17]}, {"id": "9518", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9789", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 1]}, {"id": "88", "type": [1, 0, 0, 0], "emotion": 8, "goal": [8, 0]}, {"id": "3178", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "18562", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1944", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "8752", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 19]}, {"id": "9540", "type": [1, 0, 1, 1], "emotion": 0, "goal": [0, 11]}, {"id": "14964", "type": [1, 0, 1, 1], "emotion": 2, "goal": [4, 5, 15]}, {"id": "18865", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 16]}, {"id": "15746", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 8]}, {"id": "3557", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 16]}, {"id": "13488", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 4]}, {"id": "10151", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "13318", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "17649", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16564", "type": [1, 1, 0, 0], "emotion": 8, "goal": [3, 0]}, {"id": "17880", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "636", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13, 3]}, {"id": "8007", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19706", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "12397", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "16323", "type": [1, 1, 0, 0], "emotion": 2, "goal": [18, 15]}, {"id": "8178", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17436", "type": [1, 0, 0, 0], "emotion": 2, "goal": [0, 11]}, {"id": "1192", "type": [1, 0, 0, 0], "emotion": 4, "goal": [7, 3]}, {"id": "12334", "type": [1, 0, 0, 0], "emotion": 4, "goal": [0, 3]}, {"id": "880", "type": [1, 1, 0, 0], "emotion": 0, "goal": [13, 12, 0, 3, 11]}, {"id": "3307", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "12191", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "3144", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "13861", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 8, 3]}, {"id": "11995", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 20]}, {"id": "8005", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 4, 5]}, {"id": "7923", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12733", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "15880", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17756", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 6, 15]}, {"id": "10278", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13840", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 15, 6]}, {"id": "15025", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "7008", "type": [1, 0, 1, 0], "emotion": 4, "goal": [11, 16]}, {"id": "8852", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "10229", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "13912", "type": [1, 1, 0, 0], "emotion": 1, "goal": [5, 15, 12]}, {"id": "11300", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 0]}, {"id": "1414", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 3]}, {"id": "16552", "type": [1, 0, 0, 0], "emotion": 5, "goal": [10, 2, 3]}, {"id": "11408", "type": [1, 0, 0, 0], "emotion": 4, "goal": [4, 3]}, {"id": "6442", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6533", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 13, 15, 3]}, {"id": "12022", "type": [1, 1, 1, 0], "emotion": 4, "goal": [13, 16]}, {"id": "4015", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 11, 1, 3]}, {"id": "8144", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 6]}, {"id": "12525", "type": [1, 0, 0, 0], "emotion": 2, "goal": [11, 3]}, {"id": "16803", "type": [1, 1, 0, 0], "emotion": 6, "goal": [13, 17]}, {"id": "14621", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 20]}, {"id": "5130", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 9, 3]}, {"id": "8728", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 3, 17]}, {"id": "12847", "type": [1, 0, 0, 0], "emotion": 4, "goal": [10, 13]}, {"id": "2150", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13]}, {"id": "6850", "type": [1, 0, 1, 1], "emotion": 4, "goal": [8, 13, 3]}, {"id": "10398", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4448", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "5530", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 2]}, {"id": "1406", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "11213", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 15]}, {"id": "19788", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "676", "type": [1, 1, 0, 0], "emotion": 7, "goal": [3]}, {"id": "9599", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13, 6]}, {"id": "5041", "type": [1, 1, 1, 1], "emotion": 5, "goal": [6, 15, 5]}, {"id": "7493", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 20, 15]}, {"id": "13215", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 16]}, {"id": "1951", "type": [1, 0, 0, 0], "emotion": 0, "goal": [11, 16]}, {"id": "18166", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "7078", "type": [1, 1, 0, 0], "emotion": 4, "goal": [8, 2]}, {"id": "13643", "type": [1, 0, 1, 1], "emotion": 4, "goal": [4, 18, 13, 3]}, {"id": "5908", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "2164", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 4, 5, 15]}, {"id": "1741", "type": [1, 0, 1, 0], "emotion": 4, "goal": [8, 3, 2]}, {"id": "5475", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 16]}, {"id": "5745", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 10]}, {"id": "5355", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "6865", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5626", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "359", "type": [1, 1, 0, 0], "emotion": 2, "goal": [10, 6]}, {"id": "10895", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 11]}, {"id": "6176", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "1686", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "8681", "type": [1, 0, 1, 0], "emotion": 2, "goal": [15, 10, 3]}, {"id": "5408", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 11]}, {"id": "7018", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "6632", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15, 13, 3]}, {"id": "4884", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 5, 6, 15]}, {"id": "10241", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 9]}, {"id": "13732", "type": [1, 0, 1, 1], "emotion": 7, "goal": [13, 7]}, {"id": "2341", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 10]}, {"id": "16885", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "11771", "type": [1, 0, 1, 1], "emotion": 4, "goal": [12, 16]}, {"id": "14231", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "17742", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1388", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "8398", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "16762", "type": [1, 1, 0, 0], "emotion": 5, "goal": [12, 3, 13]}, {"id": "9957", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13, 3]}, {"id": "18931", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "10193", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13, 3]}, {"id": "5718", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "19987", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5208", "type": [1, 0, 1, 1], "emotion": 6, "goal": [17, 13, 4, 3]}, {"id": "11662", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 3]}, {"id": "4114", "type": [1, 1, 0, 0], "emotion": 6, "goal": [3, 17]}, {"id": "16440", "type": [1, 0, 1, 0], "emotion": 0, "goal": [11, 0, 3]}, {"id": "7971", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 0]}, {"id": "14021", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "9064", "type": [1, 0, 0, 0], "emotion": 1, "goal": [8, 3, 13]}, {"id": "10613", "type": [1, 0, 0, 0], "emotion": 4, "goal": [4, 13]}, {"id": "6038", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "17038", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "4808", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 15, 19]}, {"id": "8861", "type": [1, 1, 0, 0], "emotion": 6, "goal": [4, 13]}, {"id": "9552", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 4]}, {"id": "19330", "type": [1, 1, 0, 0], "emotion": 5, "goal": [15, 3]}, {"id": "4431", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 16]}, {"id": "5345", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 5, 6, 15, 13]}, {"id": "622", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 8]}, {"id": "8545", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6542", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 20]}, {"id": "5413", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 15]}, {"id": "16482", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "18554", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 5, 15]}, {"id": "4396", "type": [1, 0, 0, 0], "emotion": 4, "goal": [20, 7]}, {"id": "16737", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "14961", "type": [1, 1, 0, 0], "emotion": 1, "goal": [8, 10, 19]}, {"id": "9801", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "0", "type": [1, 0, 1, 0], "emotion": 0, "goal": [0, 1]}, {"id": "19393", "type": [1, 1, 0, 0], "emotion": 0, "goal": [11, 3]}, {"id": "11265", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 16]}, {"id": "13167", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 10, 3]}, {"id": "2228", "type": [1, 0, 1, 0], "emotion": 4, "goal": [7, 15]}, {"id": "9075", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 4, 5, 15]}, {"id": "12200", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "11001", "type": [1, 1, 0, 0], "emotion": 4, "goal": [10, 15]}, {"id": "16055", "type": [1, 0, 0, 0], "emotion": 4, "goal": [8, 3]}, {"id": "2517", "type": [1, 1, 0, 0], "emotion": 2, "goal": [18, 5, 6, 15, 10]}, {"id": "13661", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "5931", "type": [1, 1, 0, 0], "emotion": 4, "goal": [11, 16]}, {"id": "4092", "type": [1, 1, 0, 0], "emotion": 2, "goal": [18, 4, 15, 20]}, {"id": "3562", "type": [1, 0, 1, 1], "emotion": 4, "goal": [11, 13, 4]}, {"id": "19858", "type": [1, 0, 0, 0], "emotion": 2, "goal": [3, 10]}, {"id": "5465", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3, 9]}, {"id": "18193", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 3]}, {"id": "1624", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 6, 15, 10]}, {"id": "19439", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "17296", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "8356", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "5996", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 16]}, {"id": "12535", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "18597", "type": [1, 0, 1, 1], "emotion": 4, "goal": [18, 13]}, {"id": "5701", "type": [1, 1, 0, 0], "emotion": 8, "goal": [12, 0]}, {"id": "8973", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "7966", "type": [1, 0, 1, 0], "emotion": 4, "goal": [19, 5, 15]}, {"id": "15857", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "13516", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "10143", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 13, 3]}, {"id": "9587", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 19]}, {"id": "15020", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "4282", "type": [1, 0, 0, 0], "emotion": 4, "goal": [7, 14]}, {"id": "13185", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1470", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 2]}, {"id": "14648", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14595", "type": [1, 0, 0, 0], "emotion": 4, "goal": [18, 10, 3]}, {"id": "18008", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12243", "type": [1, 0, 0, 0], "emotion": 8, "goal": [12, 1, 0, 11]}, {"id": "9044", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "7716", "type": [1, 0, 0, 0], "emotion": 4, "goal": [19, 20]}, {"id": "16498", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "17439", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 11]}, {"id": "1949", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 11, 3]}, {"id": "11410", "type": [1, 0, 0, 0], "emotion": 8, "goal": [0, 3, 1]}, {"id": "128", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 4, 17, 18]}, {"id": "1236", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 6, 20]}, {"id": "7873", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "346", "type": [1, 0, 0, 0], "emotion": 0, "goal": [11, 0]}, {"id": "10290", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "11452", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17232", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "1258", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "7413", "type": [1, 0, 1, 0], "emotion": 6, "goal": [17, 13]}, {"id": "8240", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13]}, {"id": "165", "type": [1, 1, 0, 0], "emotion": 1, "goal": [3]}, {"id": "12163", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "7752", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "16439", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "4134", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 4, 15, 6]}, {"id": "8152", "type": [1, 0, 1, 1], "emotion": 4, "goal": [4, 3]}, {"id": "3250", "type": [1, 1, 0, 0], "emotion": 1, "goal": [18, 13, 3]}, {"id": "1120", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 6, 4]}, {"id": "4450", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "7263", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9]}, {"id": "20041", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "6762", "type": [1, 1, 0, 0], "emotion": 4, "goal": [5, 19, 15, 3]}, {"id": "15685", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "2392", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "10086", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9252", "type": [1, 0, 1, 1], "emotion": 1, "goal": [3, 20]}, {"id": "6658", "type": [1, 0, 0, 0], "emotion": 5, "goal": [4, 6, 15]}, {"id": "4538", "type": [1, 0, 1, 0], "emotion": 6, "goal": [18, 3]}, {"id": "2390", "type": [1, 1, 0, 0], "emotion": 4, "goal": [7, 16]}, {"id": "17366", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 11]}, {"id": "9225", "type": [1, 1, 0, 0], "emotion": 4, "goal": [20, 3, 13, 4, 15]}, {"id": "2512", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15, 4]}, {"id": "6338", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "5390", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "13655", "type": [1, 1, 0, 0], "emotion": 8, "goal": [0, 11, 1, 3]}, {"id": "13793", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14330", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "20160", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "15924", "type": [1, 0, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "1527", "type": [1, 1, 0, 0], "emotion": 4, "goal": [8, 2]}, {"id": "19235", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "4160", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "5585", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 18]}, {"id": "13871", "type": [1, 0, 0, 0], "emotion": 2, "goal": [15, 6, 10]}, {"id": "18447", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "9615", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 6]}, {"id": "18903", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 3]}, {"id": "476", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "580", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "11849", "type": [1, 0, 1, 1], "emotion": 4, "goal": [18, 12, 4]}, {"id": "3403", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "17587", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3]}, {"id": "2664", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "8844", "type": [1, 1, 0, 0], "emotion": 7, "goal": [3, 7]}, {"id": "3966", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 15]}, {"id": "7834", "type": [1, 0, 0, 0], "emotion": 1, "goal": [13, 5, 3]}, {"id": "9502", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13]}, {"id": "14740", "type": [1, 0, 1, 0], "emotion": 4, "goal": [8, 3]}, {"id": "16843", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 3]}, {"id": "9345", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "216", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 6, 15]}, {"id": "13681", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 13]}, {"id": "17955", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15, 4]}, {"id": "1721", "type": [1, 0, 1, 0], "emotion": 0, "goal": [0, 11]}, {"id": "16106", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 3]}, {"id": "9160", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 4]}, {"id": "18332", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 10]}, {"id": "13452", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6378", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "13911", "type": [1, 1, 0, 0], "emotion": 8, "goal": [0, 11, 3, 1]}, {"id": "2821", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "19702", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 13]}, {"id": "5750", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "7456", "type": [1, 1, 0, 0], "emotion": 2, "goal": [7, 15, 3]}, {"id": "16558", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "16316", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13, 3]}, {"id": "12803", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "15179", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3146", "type": [1, 0, 0, 0], "emotion": 1, "goal": [7, 15, 14, 3]}, {"id": "5525", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 17, 5, 15]}, {"id": "3139", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "10059", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19256", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13888", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5826", "type": [1, 0, 1, 1], "emotion": 4, "goal": [7, 16]}, {"id": "8432", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 15, 6, 10, 3]}, {"id": "13078", "type": [1, 0, 1, 0], "emotion": 4, "goal": [18, 3]}, {"id": "11793", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "206", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 6, 2]}, {"id": "19", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 10]}, {"id": "7021", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13817", "type": [1, 1, 0, 0], "emotion": 4, "goal": [8, 3]}, {"id": "14377", "type": [1, 0, 1, 1], "emotion": 0, "goal": [4, 3, 11]}, {"id": "13100", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "13296", "type": [1, 1, 0, 0], "emotion": 5, "goal": [3, 17]}, {"id": "2376", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 11]}, {"id": "6167", "type": [1, 1, 0, 0], "emotion": 9, "goal": [12, 11]}, {"id": "2145", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "2234", "type": [1, 0, 1, 0], "emotion": 6, "goal": [17, 16]}, {"id": "9302", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "3514", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 6, 15]}, {"id": "11840", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "9544", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "7515", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12, 5]}, {"id": "18782", "type": [1, 1, 0, 0], "emotion": 4, "goal": [0, 3]}, {"id": "15242", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10, 13]}, {"id": "14099", "type": [1, 1, 0, 0], "emotion": 1, "goal": [13, 9, 3]}, {"id": "1037", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "3669", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 8]}, {"id": "1816", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "7118", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "4361", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 1]}, {"id": "2549", "type": [1, 0, 1, 1], "emotion": 4, "goal": [3]}, {"id": "13073", "type": [1, 0, 0, 0], "emotion": 8, "goal": [12, 0]}, {"id": "7238", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "15591", "type": [1, 0, 0, 0], "emotion": 5, "goal": [13, 16]}, {"id": "1126", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "17944", "type": [1, 0, 0, 0], "emotion": 1, "goal": [3, 20, 13]}, {"id": "2645", "type": [1, 0, 1, 1], "emotion": 3, "goal": [13, 3]}, {"id": "16759", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "16670", "type": [1, 0, 1, 0], "emotion": 7, "goal": [7, 16]}, {"id": "2078", "type": [1, 0, 0, 0], "emotion": 0, "goal": [12, 4, 1, 0]}, {"id": "932", "type": [1, 1, 0, 0], "emotion": 1, "goal": [10, 14]}, {"id": "3894", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 12]}, {"id": "15027", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 3, 20]}, {"id": "12712", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 3]}, {"id": "1797", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 11]}, {"id": "5009", "type": [1, 1, 0, 0], "emotion": 3, "goal": [0, 11]}, {"id": "1101", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 20]}, {"id": "5195", "type": [1, 1, 0, 0], "emotion": 4, "goal": [5, 15, 13, 3]}, {"id": "11565", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "7019", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 11]}, {"id": "3526", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 20]}, {"id": "19719", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "7192", "type": [1, 0, 0, 0], "emotion": 4, "goal": [8, 3]}, {"id": "11345", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 3]}, {"id": "7274", "type": [1, 0, 0, 0], "emotion": 2, "goal": [3, 15]}, {"id": "620", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 18, 3]}, {"id": "3985", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "3861", "type": [1, 0, 0, 0], "emotion": 4, "goal": [0, 11]}, {"id": "2331", "type": [1, 1, 0, 0], "emotion": 4, "goal": [16]}, {"id": "10994", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19923", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13, 4]}, {"id": "17261", "type": [1, 1, 0, 0], "emotion": 4, "goal": [7, 13]}, {"id": "10292", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4]}, {"id": "7231", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16649", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 3]}, {"id": "4857", "type": [1, 0, 0, 0], "emotion": 3, "goal": [18, 0, 1]}, {"id": "7564", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "13293", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "8605", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 3]}, {"id": "17712", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5945", "type": [1, 0, 0, 0], "emotion": 4, "goal": [10, 16]}, {"id": "5684", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13, 4, 12]}, {"id": "8832", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 3]}, {"id": "7324", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5283", "type": [1, 1, 0, 0], "emotion": 1, "goal": [4, 5, 3]}, {"id": "17654", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "19359", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "15544", "type": [1, 1, 0, 0], "emotion": 1, "goal": [20, 2]}, {"id": "7662", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 17]}, {"id": "13332", "type": [1, 1, 0, 0], "emotion": 8, "goal": [1, 0]}, {"id": "19540", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12911", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "18345", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6127", "type": [1, 0, 1, 1], "emotion": 4, "goal": [3, 16]}, {"id": "17204", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19003", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 8]}, {"id": "14193", "type": [1, 0, 1, 0], "emotion": 4, "goal": [12, 3]}, {"id": "13437", "type": [1, 1, 0, 0], "emotion": 4, "goal": [9, 14]}, {"id": "11597", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 6, 5]}, {"id": "1710", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "8757", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "19994", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "97", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 6, 15]}, {"id": "601", "type": [1, 1, 0, 0], "emotion": 4, "goal": [5, 15]}, {"id": "1483", "type": [1, 1, 0, 0], "emotion": 8, "goal": [6, 0]}, {"id": "9771", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "10836", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "17187", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 4]}, {"id": "2781", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 20]}, {"id": "6429", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "11726", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "8182", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "11936", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "13368", "type": [1, 0, 1, 1], "emotion": 5, "goal": [11, 4]}, {"id": "15301", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "17489", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "13971", "type": [1, 0, 0, 0], "emotion": 2, "goal": [4, 6, 15, 3]}, {"id": "18934", "type": [1, 1, 0, 0], "emotion": 4, "goal": [19, 12]}, {"id": "2476", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "5142", "type": [1, 0, 1, 1], "emotion": 4, "goal": [18, 13, 4, 3]}, {"id": "3424", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15, 5]}, {"id": "7901", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 5, 3]}, {"id": "7379", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 15]}, {"id": "14578", "type": [1, 1, 0, 0], "emotion": 9, "goal": [13, 3]}, {"id": "5720", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "17011", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "14813", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 15, 14, 3, 9]}, {"id": "17572", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "8606", "type": [1, 1, 0, 0], "emotion": 7, "goal": [6, 4, 5, 7]}, {"id": "14823", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 6, 15, 4, 5]}, {"id": "16958", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4257", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 16]}, {"id": "5109", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "15401", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 1]}, {"id": "1846", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 15, 5]}, {"id": "11526", "type": [1, 1, 0, 0], "emotion": 1, "goal": [18, 3]}, {"id": "6578", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 20]}, {"id": "7514", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "7213", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "125", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "16831", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 4, 18]}, {"id": "176", "type": [1, 1, 0, 0], "emotion": 8, "goal": [11, 0, 1]}, {"id": "10970", "type": [1, 1, 0, 0], "emotion": 7, "goal": [13, 7]}, {"id": "2953", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "3791", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "4164", "type": [1, 0, 1, 0], "emotion": 4, "goal": [18, 4, 3]}, {"id": "11607", "type": [1, 0, 1, 1], "emotion": 1, "goal": [15, 6, 3]}, {"id": "2020", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 15]}, {"id": "1203", "type": [1, 0, 0, 0], "emotion": 3, "goal": [4, 18, 11, 13]}, {"id": "19364", "type": [1, 1, 0, 0], "emotion": 2, "goal": [19, 6]}, {"id": "17288", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5189", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 14, 3]}, {"id": "15559", "type": [1, 0, 1, 0], "emotion": 5, "goal": [17, 3]}, {"id": "9847", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 5]}, {"id": "3919", "type": [1, 1, 0, 0], "emotion": 2, "goal": [18, 13, 6, 15]}, {"id": "18680", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "16021", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 10]}, {"id": "14254", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "15532", "type": [1, 0, 1, 0], "emotion": 6, "goal": [17, 13]}, {"id": "12894", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 16]}, {"id": "9780", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "231", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 11]}, {"id": "8947", "type": [1, 1, 0, 0], "emotion": 4, "goal": [19, 3]}, {"id": "2809", "type": [1, 1, 0, 0], "emotion": 6, "goal": [6, 15, 17]}, {"id": "14689", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "13397", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "5946", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 20]}, {"id": "16395", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 6]}, {"id": "1798", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 4]}, {"id": "14848", "type": [1, 0, 1, 0], "emotion": 1, "goal": [11, 3, 12, 8]}, {"id": "18258", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9246", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 2, 12]}, {"id": "1503", "type": [1, 0, 1, 1], "emotion": 5, "goal": [18, 6, 4]}, {"id": "2609", "type": [1, 1, 0, 0], "emotion": 1, "goal": [15, 3, 10]}, {"id": "6621", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 6]}, {"id": "8244", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15]}, {"id": "19876", "type": [1, 0, 1, 0], "emotion": 4, "goal": [12, 3]}, {"id": "19304", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 13, 3]}, {"id": "3698", "type": [1, 0, 0, 0], "emotion": 0, "goal": [13, 0, 11]}, {"id": "3531", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3010", "type": [1, 0, 1, 0], "emotion": 1, "goal": [13, 3, 14]}, {"id": "6821", "type": [1, 0, 0, 0], "emotion": 1, "goal": [13, 3, 11]}, {"id": "18077", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "2564", "type": [1, 0, 1, 1], "emotion": 1, "goal": [3, 12]}, {"id": "7256", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 3]}, {"id": "10243", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12479", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 4]}, {"id": "8507", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12, 3]}, {"id": "11934", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 16]}, {"id": "9707", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6]}, {"id": "18446", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "7317", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15, 3]}, {"id": "12605", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4, 12]}, {"id": "16375", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 4, 3]}, {"id": "7628", "type": [1, 1, 0, 0], "emotion": 5, "goal": [17, 16]}, {"id": "16139", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3, 11]}, {"id": "10565", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "10284", "type": [1, 0, 0, 0], "emotion": 0, "goal": [3, 11]}, {"id": "4084", "type": [1, 0, 0, 0], "emotion": 2, "goal": [15, 3, 20, 6]}, {"id": "211", "type": [1, 1, 0, 0], "emotion": 4, "goal": [9, 3, 13]}, {"id": "12939", "type": [1, 1, 0, 0], "emotion": 2, "goal": [11, 16]}, {"id": "8385", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6]}, {"id": "6684", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 5]}, {"id": "6767", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 9, 3]}, {"id": "12875", "type": [1, 0, 0, 0], "emotion": 4, "goal": [18, 13]}, {"id": "18176", "type": [1, 0, 0, 0], "emotion": 7, "goal": [13, 16]}, {"id": "5247", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "9410", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 11]}, {"id": "18630", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3, 10]}, {"id": "16828", "type": [1, 0, 1, 1], "emotion": 4, "goal": [3, 16]}, {"id": "18184", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15, 9]}, {"id": "14139", "type": [1, 0, 1, 0], "emotion": 4, "goal": [8, 13]}, {"id": "2042", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 3]}, {"id": "10349", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 13]}, {"id": "10695", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 3]}, {"id": "5455", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "10369", "type": [1, 1, 0, 0], "emotion": 7, "goal": [13, 7, 3, 20]}, {"id": "19344", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3958", "type": [1, 0, 1, 0], "emotion": 6, "goal": [6, 15, 17, 3]}, {"id": "7110", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 16]}, {"id": "3236", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 15, 13, 8]}, {"id": "19056", "type": [1, 0, 0, 0], "emotion": 2, "goal": [3, 6, 4]}, {"id": "7953", "type": [1, 0, 0, 0], "emotion": 4, "goal": [15, 10]}, {"id": "1076", "type": [1, 1, 0, 0], "emotion": 1, "goal": [3]}, {"id": "14524", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 16]}, {"id": "5903", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "10122", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 3]}, {"id": "18268", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 20]}, {"id": "6511", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "19774", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "19350", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "1843", "type": [1, 1, 0, 0], "emotion": 7, "goal": [7, 4]}, {"id": "14526", "type": [1, 0, 0, 0], "emotion": 5, "goal": [3]}, {"id": "10756", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "368", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "2969", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 15]}, {"id": "11989", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1930", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 1]}, {"id": "2539", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 13, 4]}, {"id": "13016", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4]}, {"id": "16898", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13]}, {"id": "13518", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15, 3]}, {"id": "9423", "type": [1, 0, 1, 1], "emotion": 4, "goal": [17, 13, 4]}, {"id": "2966", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 4]}, {"id": "4637", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 3]}, {"id": "15440", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "665", "type": [1, 0, 0, 0], "emotion": 2, "goal": [17, 6]}, {"id": "18818", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "332", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 12]}, {"id": "5940", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "8535", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5414", "type": [1, 0, 1, 1], "emotion": 4, "goal": [18, 13]}, {"id": "7573", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 16]}, {"id": "18712", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 16]}, {"id": "14939", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "13508", "type": [1, 0, 1, 1], "emotion": 6, "goal": [18, 5, 4, 3, 17]}, {"id": "4136", "type": [1, 1, 0, 0], "emotion": 4, "goal": [16, 13]}, {"id": "5229", "type": [1, 1, 0, 0], "emotion": 5, "goal": [6, 15, 10]}, {"id": "7839", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "18914", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "1001", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 5]}, {"id": "593", "type": [1, 0, 1, 1], "emotion": 5, "goal": [6, 15, 4, 5]}, {"id": "13564", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "15845", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9522", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19671", "type": [1, 0, 0, 0], "emotion": 8, "goal": [18, 0]}, {"id": "6187", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15, 12]}, {"id": "9263", "type": [1, 1, 0, 0], "emotion": 8, "goal": [0, 1]}, {"id": "18426", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "10953", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5068", "type": [1, 0, 0, 0], "emotion": 4, "goal": [0, 11, 1]}, {"id": "10032", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19731", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 13, 10]}, {"id": "14945", "type": [1, 0, 0, 0], "emotion": 2, "goal": [15, 3]}, {"id": "2096", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "2344", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "12876", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "13748", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 15]}, {"id": "10900", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 13]}, {"id": "16641", "type": [1, 0, 0, 0], "emotion": 2, "goal": [16, 10]}, {"id": "19240", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "11055", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4278", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 15, 10]}, {"id": "10169", "type": [1, 1, 0, 0], "emotion": 4, "goal": [5, 15, 3]}, {"id": "18859", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 11, 3]}, {"id": "13951", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 5, 6, 15, 10]}, {"id": "5925", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "15271", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "5406", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4]}, {"id": "5296", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "4822", "type": [1, 1, 0, 0], "emotion": 5, "goal": [13, 4, 15]}, {"id": "4419", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "5665", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13]}, {"id": "17500", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "16300", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 15, 3]}, {"id": "18372", "type": [1, 1, 0, 0], "emotion": 3, "goal": [13, 16]}, {"id": "3476", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9]}, {"id": "985", "type": [1, 0, 1, 0], "emotion": 4, "goal": [15, 16]}, {"id": "9116", "type": [1, 0, 0, 0], "emotion": 8, "goal": [0, 1]}, {"id": "12615", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3061", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "7260", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 3]}, {"id": "17888", "type": [1, 0, 1, 0], "emotion": 4, "goal": [16]}, {"id": "6240", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 12]}, {"id": "6328", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "11776", "type": [1, 1, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "19685", "type": [1, 1, 0, 0], "emotion": 0, "goal": [0, 3]}, {"id": "10082", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 13, 8]}, {"id": "16090", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "11464", "type": [1, 0, 0, 0], "emotion": 4, "goal": [14, 3]}, {"id": "16465", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5987", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6]}, {"id": "15203", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 6, 15]}, {"id": "3374", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "5232", "type": [1, 1, 0, 0], "emotion": 4, "goal": [5, 12]}, {"id": "11904", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "2787", "type": [1, 1, 0, 0], "emotion": 2, "goal": [10, 15]}, {"id": "19332", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5096", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "7903", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "7343", "type": [1, 0, 0, 0], "emotion": 4, "goal": [20, 3]}, {"id": "2043", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 4]}, {"id": "3672", "type": [1, 0, 1, 1], "emotion": 4, "goal": [11, 0]}, {"id": "19128", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "18837", "type": [1, 0, 1, 0], "emotion": 2, "goal": [8, 6]}, {"id": "137", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 11]}, {"id": "11786", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16780", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "9240", "type": [1, 0, 1, 0], "emotion": 4, "goal": [5, 13, 3]}, {"id": "13984", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 12, 0]}, {"id": "14858", "type": [1, 1, 1, 0], "emotion": 4, "goal": [4, 13, 3]}, {"id": "7271", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 4, 15, 10]}, {"id": "5438", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "16760", "type": [1, 1, 0, 0], "emotion": 0, "goal": [12, 11]}, {"id": "6719", "type": [1, 0, 1, 0], "emotion": 1, "goal": [7, 15, 5]}, {"id": "14758", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3973", "type": [1, 0, 1, 0], "emotion": 2, "goal": [11, 4]}, {"id": "12570", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4045", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 4]}, {"id": "4638", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "8437", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 16]}, {"id": "18295", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "20046", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "18191", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "11534", "type": [1, 0, 1, 0], "emotion": 5, "goal": [3, 13]}, {"id": "8012", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 15, 10, 4]}, {"id": "12482", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14831", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "10794", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 16]}, {"id": "12621", "type": [1, 1, 1, 1], "emotion": 2, "goal": [13, 18, 6]}, {"id": "15904", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3642", "type": [1, 0, 1, 1], "emotion": 2, "goal": [17, 15]}, {"id": "3652", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "17537", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5456", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4]}, {"id": "17740", "type": [1, 1, 0, 0], "emotion": 4, "goal": [16]}, {"id": "1295", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3]}, {"id": "12928", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4]}, {"id": "9759", "type": [1, 1, 0, 0], "emotion": 4, "goal": [11, 16]}, {"id": "5742", "type": [1, 0, 1, 1], "emotion": 7, "goal": [4, 16]}, {"id": "9532", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "8260", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 16]}, {"id": "17169", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 15]}, {"id": "3182", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "1835", "type": [1, 1, 0, 0], "emotion": 6, "goal": [13, 17]}, {"id": "14253", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "1159", "type": [1, 0, 1, 0], "emotion": 2, "goal": [4, 5, 6, 15]}, {"id": "8656", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "7803", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "11084", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 11]}, {"id": "4682", "type": [1, 1, 0, 0], "emotion": 6, "goal": [3, 16]}, {"id": "19557", "type": [1, 0, 0, 0], "emotion": 4, "goal": [8, 3]}, {"id": "16570", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "14505", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 16]}, {"id": "5451", "type": [1, 1, 0, 0], "emotion": 7, "goal": [13, 3]}, {"id": "12429", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "6159", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 13]}, {"id": "7629", "type": [1, 0, 1, 1], "emotion": 6, "goal": [17, 3]}, {"id": "9690", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "18463", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 4]}, {"id": "6571", "type": [1, 1, 0, 0], "emotion": 2, "goal": [18, 4, 5, 6, 15, 3]}, {"id": "42", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13]}, {"id": "18453", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 11]}, {"id": "16817", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "10024", "type": [1, 1, 0, 0], "emotion": 4, "goal": [16]}, {"id": "15775", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 0]}, {"id": "6960", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "10933", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 16]}, {"id": "14111", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "3900", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 12]}, {"id": "2452", "type": [1, 0, 0, 0], "emotion": 3, "goal": [0, 11, 1]}, {"id": "13668", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "18620", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3, 4]}, {"id": "787", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 4, 5]}, {"id": "18231", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "13855", "type": [1, 0, 1, 0], "emotion": 2, "goal": [3, 15]}, {"id": "13890", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "5128", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 1]}, {"id": "18197", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 6]}, {"id": "8568", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13]}, {"id": "1063", "type": [1, 1, 0, 0], "emotion": 7, "goal": [19]}, {"id": "7017", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "436", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 7]}, {"id": "13673", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 3]}, {"id": "10648", "type": [1, 0, 0, 0], "emotion": 2, "goal": [20, 3]}, {"id": "592", "type": [1, 0, 0, 0], "emotion": 0, "goal": [8, 0, 11, 1]}, {"id": "2795", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "17739", "type": [1, 1, 0, 0], "emotion": 1, "goal": [11, 0, 15, 3, 1]}, {"id": "9953", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 5, 15, 14, 3]}, {"id": "15480", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13]}, {"id": "13755", "type": [1, 1, 0, 0], "emotion": 4, "goal": [19, 13, 3]}, {"id": "47", "type": [1, 1, 0, 0], "emotion": 2, "goal": [10, 20]}, {"id": "14182", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 6, 15]}, {"id": "2741", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "19182", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "11425", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "12561", "type": [1, 0, 0, 0], "emotion": 4, "goal": [0, 1, 3]}, {"id": "16433", "type": [1, 0, 1, 1], "emotion": 4, "goal": [4, 13, 3]}, {"id": "6017", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4150", "type": [1, 0, 1, 1], "emotion": 2, "goal": [3, 15]}, {"id": "7811", "type": [1, 0, 0, 0], "emotion": 2, "goal": [5, 6, 15, 13]}, {"id": "12058", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "15785", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "17431", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 18, 3]}, {"id": "12914", "type": [1, 1, 0, 0], "emotion": 4, "goal": [0, 16]}, {"id": "12907", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "2579", "type": [1, 1, 0, 0], "emotion": 1, "goal": [3, 20]}, {"id": "18431", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4867", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "7095", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "9926", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9, 3]}, {"id": "17664", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 10]}, {"id": "12287", "type": [1, 0, 1, 1], "emotion": 4, "goal": [3, 4]}, {"id": "8654", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "9708", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "15828", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 3]}, {"id": "12523", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "6628", "type": [1, 1, 0, 0], "emotion": 1, "goal": [7, 6, 5, 15, 3]}, {"id": "9383", "type": [1, 0, 0, 0], "emotion": 4, "goal": [10, 3]}, {"id": "4577", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "1570", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 6]}, {"id": "19468", "type": [1, 0, 0, 0], "emotion": 8, "goal": [13, 0]}, {"id": "3454", "type": [1, 0, 1, 0], "emotion": 8, "goal": [18, 0, 11]}, {"id": "13676", "type": [1, 0, 1, 0], "emotion": 8, "goal": [0, 1, 3]}, {"id": "4133", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "494", "type": [1, 0, 0, 0], "emotion": 4, "goal": [11, 5]}, {"id": "18273", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17170", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16707", "type": [1, 0, 1, 0], "emotion": 4, "goal": [4, 16]}, {"id": "1149", "type": [1, 0, 0, 0], "emotion": 4, "goal": [7, 10]}, {"id": "7876", "type": [1, 1, 0, 0], "emotion": 5, "goal": [13, 4, 15, 3]}, {"id": "4233", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "14005", "type": [1, 0, 0, 0], "emotion": 6, "goal": [17, 4, 15, 3]}, {"id": "10891", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "10378", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "1136", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "12584", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 15]}, {"id": "11724", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "16367", "type": [1, 0, 0, 0], "emotion": 1, "goal": [8, 13, 3, 15]}, {"id": "6365", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 3, 13]}, {"id": "5958", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 7]}, {"id": "11292", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 5]}, {"id": "2885", "type": [1, 0, 0, 0], "emotion": 0, "goal": [11, 0]}, {"id": "760", "type": [1, 0, 0, 0], "emotion": 6, "goal": [17, 3]}, {"id": "13806", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 3, 20]}, {"id": "10717", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "13127", "type": [1, 0, 1, 0], "emotion": 4, "goal": [4, 3]}, {"id": "4458", "type": [1, 0, 1, 1], "emotion": 9, "goal": [12, 16]}, {"id": "9593", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13]}, {"id": "2223", "type": [1, 0, 0, 0], "emotion": 5, "goal": [4, 6]}, {"id": "12677", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 2, 3]}, {"id": "4873", "type": [1, 0, 1, 0], "emotion": 1, "goal": [13, 4, 3]}, {"id": "3153", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 13]}, {"id": "9691", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "20048", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "18665", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 13, 4]}, {"id": "11681", "type": [1, 0, 1, 1], "emotion": 1, "goal": [16, 5]}, {"id": "1854", "type": [1, 0, 1, 1], "emotion": 6, "goal": [6, 17]}, {"id": "7079", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 4, 16]}, {"id": "4208", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "10893", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "18545", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "18584", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4]}, {"id": "2961", "type": [1, 0, 1, 0], "emotion": 8, "goal": [1, 0]}, {"id": "1496", "type": [1, 1, 0, 0], "emotion": 4, "goal": [5, 4]}, {"id": "12693", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 16]}, {"id": "1810", "type": [1, 1, 0, 0], "emotion": 0, "goal": [4, 3]}, {"id": "17657", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "15742", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "9969", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 13, 5, 6, 15]}, {"id": "13906", "type": [1, 0, 1, 1], "emotion": 5, "goal": [13, 15, 14, 9, 3]}, {"id": "4142", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "15184", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14516", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 5, 15]}, {"id": "8990", "type": [1, 1, 0, 0], "emotion": 8, "goal": [1, 0, 11]}, {"id": "2364", "type": [1, 1, 0, 0], "emotion": 2, "goal": [18, 4, 15]}, {"id": "13108", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "3164", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 9, 3]}, {"id": "5565", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 10]}, {"id": "3970", "type": [1, 1, 0, 0], "emotion": 6, "goal": [6, 17]}, {"id": "10587", "type": [1, 1, 0, 0], "emotion": 7, "goal": [7, 11]}, {"id": "16969", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "13316", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 5, 18]}, {"id": "117", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "10926", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 10, 15]}, {"id": "13917", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "2218", "type": [1, 0, 0, 0], "emotion": 4, "goal": [0, 1]}, {"id": "17721", "type": [1, 1, 0, 0], "emotion": 4, "goal": [9, 3, 13]}, {"id": "19830", "type": [1, 1, 0, 0], "emotion": 2, "goal": [10, 13, 15]}, {"id": "12623", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17681", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15, 13]}, {"id": "15684", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3]}, {"id": "8082", "type": [1, 0, 0, 0], "emotion": 2, "goal": [20, 3, 10]}, {"id": "13893", "type": [1, 1, 0, 0], "emotion": 4, "goal": [16]}, {"id": "16305", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "14102", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "9973", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 13, 4, 5, 6, 15]}, {"id": "14471", "type": [1, 1, 0, 0], "emotion": 7, "goal": [0, 3]}, {"id": "15664", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "17450", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12845", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 15, 10]}, {"id": "14637", "type": [1, 0, 0, 0], "emotion": 2, "goal": [15, 3]}, {"id": "12271", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "18814", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 6]}, {"id": "12566", "type": [1, 1, 0, 0], "emotion": 0, "goal": [11, 1, 0, 3]}, {"id": "18677", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "13736", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1644", "type": [1, 0, 0, 0], "emotion": 4, "goal": [8, 13]}, {"id": "18687", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1659", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "334", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 6]}, {"id": "18939", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "16238", "type": [1, 1, 0, 0], "emotion": 7, "goal": [3, 15, 5, 4]}, {"id": "11572", "type": [1, 1, 0, 0], "emotion": 5, "goal": [13, 5, 15, 3, 20]}, {"id": "4385", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 16]}, {"id": "6459", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "8242", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 16]}, {"id": "16733", "type": [1, 1, 0, 0], "emotion": 4, "goal": [2, 3]}, {"id": "6608", "type": [1, 1, 0, 0], "emotion": 1, "goal": [15, 3]}, {"id": "16846", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 3]}, {"id": "10332", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 9]}, {"id": "6189", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "7445", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 3]}, {"id": "12485", "type": [1, 1, 0, 0], "emotion": 4, "goal": [19, 3, 15, 4]}, {"id": "12396", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13, 3]}, {"id": "14613", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 20, 3]}, {"id": "13557", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "15815", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 13, 15]}, {"id": "10723", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "13210", "type": [1, 0, 0, 0], "emotion": 8, "goal": [11, 3]}, {"id": "16483", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 9, 13]}, {"id": "5427", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9846", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 16]}, {"id": "14289", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 3]}, {"id": "15246", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "2044", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 15]}, {"id": "14742", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19165", "type": [1, 0, 1, 0], "emotion": 7, "goal": [7, 16]}, {"id": "12741", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "19641", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 16]}, {"id": "7659", "type": [1, 0, 0, 0], "emotion": 8, "goal": [11, 3]}, {"id": "10211", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 15, 3]}, {"id": "2231", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 1]}, {"id": "19846", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "17016", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "12444", "type": [1, 1, 0, 0], "emotion": 0, "goal": [1, 0, 3]}, {"id": "9209", "type": [1, 0, 0, 0], "emotion": 4, "goal": [18, 13, 8, 11]}, {"id": "7043", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "12717", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "7201", "type": [1, 0, 0, 0], "emotion": 7, "goal": [12, 3]}, {"id": "15783", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 7]}, {"id": "17983", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13919", "type": [1, 0, 0, 0], "emotion": 4, "goal": [0, 14, 9, 3]}, {"id": "11958", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "9262", "type": [1, 1, 0, 0], "emotion": 7, "goal": [7, 3]}, {"id": "8724", "type": [1, 1, 0, 0], "emotion": 7, "goal": [17, 12, 11, 13]}, {"id": "1391", "type": [1, 1, 0, 0], "emotion": 2, "goal": [18, 20]}, {"id": "11343", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 7]}, {"id": "8999", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "1901", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "8215", "type": [1, 0, 0, 0], "emotion": 4, "goal": [0, 3]}, {"id": "7822", "type": [1, 0, 0, 0], "emotion": 2, "goal": [15, 6]}, {"id": "6050", "type": [1, 1, 0, 0], "emotion": 5, "goal": [3, 16, 17]}, {"id": "19289", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3604", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 16]}, {"id": "1779", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 4]}, {"id": "452", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 17]}, {"id": "7096", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 0]}, {"id": "12688", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "17697", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "766", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "3786", "type": [1, 0, 1, 1], "emotion": 2, "goal": [4, 3]}, {"id": "13792", "type": [1, 1, 0, 0], "emotion": 7, "goal": [6, 15, 3]}, {"id": "12761", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "8592", "type": [1, 1, 0, 0], "emotion": 4, "goal": [6, 13, 15]}, {"id": "7859", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12815", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "957", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 5]}, {"id": "10586", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "11302", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "4204", "type": [1, 0, 0, 0], "emotion": 4, "goal": [4, 8]}, {"id": "3945", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "7164", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "11180", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "844", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 2, 15, 6]}, {"id": "4710", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "4241", "type": [1, 0, 1, 0], "emotion": 5, "goal": [13, 16]}, {"id": "10437", "type": [1, 1, 0, 0], "emotion": 4, "goal": [14, 20]}, {"id": "17597", "type": [1, 1, 0, 0], "emotion": 7, "goal": [13, 16]}, {"id": "16678", "type": [1, 0, 1, 1], "emotion": 4, "goal": [3, 16]}, {"id": "8281", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 18]}, {"id": "4586", "type": [1, 0, 1, 0], "emotion": 2, "goal": [3, 10]}, {"id": "18366", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "6073", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "18245", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "15870", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "4494", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 12]}, {"id": "17948", "type": [1, 0, 1, 1], "emotion": 5, "goal": [18, 3]}, {"id": "4610", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "20037", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 16]}, {"id": "19405", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 12, 3]}, {"id": "1482", "type": [1, 0, 1, 1], "emotion": 2, "goal": [12, 6, 4, 5]}, {"id": "4926", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13]}, {"id": "20129", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "6403", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "19169", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 3, 19]}, {"id": "12369", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4, 3]}, {"id": "13507", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 3]}, {"id": "9192", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 15, 13, 3]}, {"id": "10498", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "8346", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "11816", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5432", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4]}, {"id": "11234", "type": [1, 1, 0, 0], "emotion": 4, "goal": [8, 13]}, {"id": "18224", "type": [1, 0, 0, 0], "emotion": 0, "goal": [3, 0]}, {"id": "15979", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "11671", "type": [1, 0, 0, 0], "emotion": 4, "goal": [0, 12]}, {"id": "20020", "type": [1, 1, 0, 0], "emotion": 5, "goal": [4, 13, 3]}, {"id": "11119", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 10]}, {"id": "11199", "type": [1, 1, 0, 0], "emotion": 2, "goal": [7, 6]}, {"id": "260", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 7]}, {"id": "13780", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "2617", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 5, 6, 15]}, {"id": "9848", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 16]}, {"id": "3282", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 13]}, {"id": "16518", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "4969", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "9438", "type": [1, 1, 0, 0], "emotion": 5, "goal": [13, 4, 15]}, {"id": "1742", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "7464", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "12793", "type": [1, 0, 0, 0], "emotion": 4, "goal": [6, 3]}, {"id": "19436", "type": [1, 0, 1, 1], "emotion": 4, "goal": [8, 16]}, {"id": "15310", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "12407", "type": [1, 0, 0, 0], "emotion": 1, "goal": [4, 1, 3]}, {"id": "19801", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1314", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "13758", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "6179", "type": [1, 1, 0, 0], "emotion": 6, "goal": [18, 13]}, {"id": "10641", "type": [1, 1, 0, 0], "emotion": 1, "goal": [7, 4]}, {"id": "374", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "2239", "type": [1, 0, 0, 0], "emotion": 4, "goal": [7]}, {"id": "943", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 10]}, {"id": "5147", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "2012", "type": [1, 0, 1, 1], "emotion": 2, "goal": [15, 10, 6]}, {"id": "11950", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "7181", "type": [1, 1, 0, 0], "emotion": 8, "goal": [12, 16]}, {"id": "2929", "type": [1, 1, 0, 0], "emotion": 6, "goal": [20, 13]}, {"id": "7658", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 3]}, {"id": "13416", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "3818", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 4, 5, 15]}, {"id": "4789", "type": [1, 0, 0, 0], "emotion": 8, "goal": [0, 11, 13]}, {"id": "7789", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "16841", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "16530", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "4478", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 4, 12]}, {"id": "12431", "type": [1, 1, 0, 0], "emotion": 9, "goal": [1, 12, 18]}, {"id": "2065", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "13798", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 1, 3]}, {"id": "16247", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "12543", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "18167", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15, 13, 20]}, {"id": "10549", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "7938", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3352", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 6, 5]}, {"id": "19730", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "6385", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "12110", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "13324", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 3, 4]}, {"id": "16427", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "399", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6]}, {"id": "1833", "type": [1, 0, 1, 1], "emotion": 6, "goal": [0, 4]}, {"id": "1572", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "4713", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "10974", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 15]}, {"id": "9048", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 9, 14, 3]}, {"id": "18287", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 10, 15, 3]}, {"id": "6470", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 20]}, {"id": "3528", "type": [1, 1, 0, 0], "emotion": 0, "goal": [13, 11, 0]}, {"id": "7745", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "1297", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 15]}, {"id": "8236", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 15]}, {"id": "1515", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 5]}, {"id": "953", "type": [1, 1, 0, 0], "emotion": 1, "goal": [19, 12]}, {"id": "15017", "type": [1, 0, 1, 1], "emotion": 2, "goal": [15, 10, 5, 4, 20]}, {"id": "423", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "14571", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "5572", "type": [1, 1, 0, 0], "emotion": 4, "goal": [8, 6, 3]}, {"id": "14151", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "4220", "type": [1, 0, 0, 0], "emotion": 9, "goal": [4, 16]}, {"id": "20131", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 5, 15, 13, 20]}, {"id": "11144", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "14034", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1767", "type": [1, 0, 1, 1], "emotion": 4, "goal": [20, 16]}, {"id": "9851", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 16]}, {"id": "14183", "type": [1, 0, 1, 0], "emotion": 9, "goal": [3]}, {"id": "1355", "type": [1, 1, 0, 0], "emotion": 5, "goal": [17, 6]}, {"id": "6796", "type": [1, 1, 0, 0], "emotion": 1, "goal": [4, 15, 19]}, {"id": "18945", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 4]}, {"id": "1221", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "14395", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "9402", "type": [1, 0, 0, 0], "emotion": 7, "goal": [13, 3]}, {"id": "19963", "type": [1, 0, 1, 0], "emotion": 2, "goal": [18, 13, 6, 12]}, {"id": "16907", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "5416", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "15594", "type": [1, 1, 0, 0], "emotion": 5, "goal": [3, 16]}, {"id": "10789", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "13777", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "5339", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 3, 1]}, {"id": "11396", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13]}, {"id": "18119", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "3560", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15]}, {"id": "5961", "type": [1, 1, 0, 0], "emotion": 8, "goal": [12, 16]}, {"id": "18899", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 13]}, {"id": "4390", "type": [1, 0, 1, 1], "emotion": 0, "goal": [0, 11]}, {"id": "3510", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4]}, {"id": "12882", "type": [1, 1, 0, 0], "emotion": 4, "goal": [10, 3]}, {"id": "19450", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 11]}, {"id": "8517", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "1362", "type": [1, 0, 0, 0], "emotion": 4, "goal": [7, 4]}, {"id": "5000", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "4103", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3, 11, 15]}, {"id": "19771", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "11848", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 3]}, {"id": "11398", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "16776", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 6]}, {"id": "13870", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 3]}, {"id": "18242", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13489", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "14962", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 3]}, {"id": "12823", "type": [1, 0, 1, 1], "emotion": 2, "goal": [6, 3]}, {"id": "15565", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6208", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6712", "type": [1, 0, 1, 1], "emotion": 5, "goal": [13, 4, 15]}, {"id": "14440", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 3]}, {"id": "5218", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 3, 1]}, {"id": "18523", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15, 12]}, {"id": "8320", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 10]}, {"id": "10261", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "10919", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 11]}, {"id": "16064", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3, 20]}, {"id": "13537", "type": [1, 0, 0, 0], "emotion": 4, "goal": [20, 3]}, {"id": "194", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 12, 3, 2]}, {"id": "13630", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "2293", "type": [1, 1, 0, 0], "emotion": 2, "goal": [11, 16]}, {"id": "3760", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 17]}, {"id": "1224", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 4, 3]}, {"id": "9465", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 13, 3]}, {"id": "10826", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 4, 18]}, {"id": "1077", "type": [1, 0, 0, 0], "emotion": 0, "goal": [1, 0]}, {"id": "13585", "type": [1, 1, 0, 0], "emotion": 2, "goal": [17, 13, 3, 15]}, {"id": "15652", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "6942", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "448", "type": [1, 0, 0, 0], "emotion": 8, "goal": [18, 4, 0]}, {"id": "6313", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 3]}, {"id": "6431", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "20149", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "5972", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "12011", "type": [1, 0, 0, 0], "emotion": 8, "goal": [1, 0]}, {"id": "8447", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17299", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "7951", "type": [1, 1, 0, 0], "emotion": 2, "goal": [15, 6]}, {"id": "7693", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "16398", "type": [1, 0, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "414", "type": [1, 1, 0, 0], "emotion": 5, "goal": [18, 17, 4]}, {"id": "18032", "type": [1, 0, 1, 0], "emotion": 2, "goal": [13, 15, 4, 20]}, {"id": "1231", "type": [1, 0, 0, 0], "emotion": 1, "goal": [3, 10, 20]}, {"id": "16002", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3]}, {"id": "4545", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "3205", "type": [1, 1, 0, 0], "emotion": 2, "goal": [5, 6, 15, 10]}, {"id": "14378", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6]}, {"id": "4377", "type": [1, 1, 0, 0], "emotion": 4, "goal": [15, 16]}, {"id": "9787", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "9473", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 4]}, {"id": "8829", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 4, 3]}, {"id": "7316", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "7381", "type": [1, 0, 1, 0], "emotion": 0, "goal": [0, 1, 11]}, {"id": "8435", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 4, 15]}, {"id": "2115", "type": [1, 1, 0, 0], "emotion": 4, "goal": [18, 4, 13]}, {"id": "15911", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 16]}, {"id": "4337", "type": [1, 0, 1, 0], "emotion": 2, "goal": [19, 6, 15]}, {"id": "6107", "type": [1, 0, 1, 0], "emotion": 5, "goal": [18, 3]}, {"id": "16009", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13]}, {"id": "10399", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 3]}, {"id": "4909", "type": [1, 1, 0, 0], "emotion": 1, "goal": [6, 15]}, {"id": "7363", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "2750", "type": [1, 0, 0, 0], "emotion": 2, "goal": [18, 13]}, {"id": "18855", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 6, 3]}, {"id": "13180", "type": [1, 0, 0, 0], "emotion": 4, "goal": [18, 13, 3]}, {"id": "8183", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 16]}, {"id": "371", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6]}, {"id": "4519", "type": [1, 1, 0, 0], "emotion": 9, "goal": [13, 16]}, {"id": "7054", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 5]}, {"id": "15092", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 15]}, {"id": "11610", "type": [1, 0, 1, 0], "emotion": 5, "goal": [3, 20]}, {"id": "6222", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "20008", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3, 2]}, {"id": "7270", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "1956", "type": [1, 0, 0, 0], "emotion": 2, "goal": [18, 8, 6, 15]}, {"id": "10244", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "18786", "type": [1, 0, 1, 1], "emotion": 4, "goal": [13, 16]}, {"id": "15463", "type": [1, 0, 1, 1], "emotion": 4, "goal": [10, 3]}, {"id": "7202", "type": [1, 1, 0, 0], "emotion": 6, "goal": [17, 13, 3]}, {"id": "12010", "type": [1, 0, 0, 0], "emotion": 2, "goal": [17, 3]}, {"id": "6743", "type": [1, 1, 0, 0], "emotion": 1, "goal": [8, 13, 3]}, {"id": "3582", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "18048", "type": [1, 0, 1, 1], "emotion": 6, "goal": [17, 13]}, {"id": "3910", "type": [1, 1, 0, 0], "emotion": 0, "goal": [12, 11]}, {"id": "19856", "type": [1, 0, 0, 0], "emotion": 4, "goal": [6, 3]}, {"id": "18910", "type": [1, 0, 1, 0], "emotion": 1, "goal": [17, 4]}, {"id": "10811", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "1299", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 5]}, {"id": "9346", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "13504", "type": [1, 1, 0, 0], "emotion": 5, "goal": [3, 12]}, {"id": "10145", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 15, 14, 9, 3]}, {"id": "12540", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13, 3]}, {"id": "16510", "type": [1, 0, 0, 0], "emotion": 5, "goal": [3, 16]}, {"id": "12816", "type": [1, 0, 0, 0], "emotion": 4, "goal": [18, 13, 3]}, {"id": "16262", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "19161", "type": [1, 1, 0, 0], "emotion": 4, "goal": [17, 19]}, {"id": "15913", "type": [1, 0, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "15786", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "5806", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "12175", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "8748", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "17749", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 16]}, {"id": "5044", "type": [1, 0, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "18760", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "1202", "type": [1, 0, 1, 0], "emotion": 1, "goal": [19, 17]}, {"id": "8684", "type": [1, 0, 1, 0], "emotion": 4, "goal": [18, 13, 4, 5, 15]}, {"id": "6053", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 5, 4]}, {"id": "5367", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "1702", "type": [1, 0, 0, 0], "emotion": 4, "goal": [8, 0, 11]}, {"id": "5104", "type": [1, 0, 1, 0], "emotion": 2, "goal": [15, 6, 10]}, {"id": "4201", "type": [1, 0, 1, 0], "emotion": 2, "goal": [6, 15]}, {"id": "864", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13439", "type": [1, 1, 0, 0], "emotion": 6, "goal": [13, 15]}, {"id": "17135", "type": [1, 0, 1, 1], "emotion": 2, "goal": [13, 4]}, {"id": "17494", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "1274", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "16004", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 18, 3]}, {"id": "1173", "type": [1, 0, 0, 0], "emotion": 4, "goal": [4, 3]}, {"id": "8738", "type": [1, 1, 0, 0], "emotion": 2, "goal": [3, 14]}, {"id": "1161", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "1016", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 5, 4, 15]}, {"id": "18118", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "3446", "type": [1, 0, 0, 0], "emotion": 2, "goal": [13, 4, 15]}, {"id": "5975", "type": [1, 0, 1, 0], "emotion": 4, "goal": [18, 13]}, {"id": "15890", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 18]}, {"id": "1183", "type": [1, 0, 0, 0], "emotion": 0, "goal": [0, 5]}, {"id": "16074", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "19566", "type": [1, 0, 1, 0], "emotion": 8, "goal": [0, 3]}, {"id": "4272", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "9600", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "16929", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15]}, {"id": "7241", "type": [1, 1, 0, 0], "emotion": 0, "goal": [12, 11, 1]}, {"id": "12703", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3]}, {"id": "19580", "type": [1, 1, 0, 0], "emotion": 4, "goal": [4, 13, 3]}, {"id": "4400", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 5]}, {"id": "3772", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "9766", "type": [1, 1, 0, 0], "emotion": 4, "goal": [12, 16]}, {"id": "12768", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "8985", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 10]}, {"id": "6370", "type": [1, 0, 0, 0], "emotion": 6, "goal": [3, 17]}, {"id": "372", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "15939", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13]}, {"id": "13621", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "6432", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 16]}, {"id": "6134", "type": [1, 0, 1, 1], "emotion": 6, "goal": [17, 13]}, {"id": "5712", "type": [1, 0, 1, 0], "emotion": 4, "goal": [3, 0]}, {"id": "15106", "type": [1, 1, 0, 0], "emotion": 2, "goal": [13, 4, 15]}, {"id": "15043", "type": [1, 0, 0, 0], "emotion": 0, "goal": [11, 20, 0]}, {"id": "1285", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "16399", "type": [1, 1, 0, 0], "emotion": 2, "goal": [20, 3, 4, 13, 15]}, {"id": "8118", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 15]}, {"id": "12643", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "15183", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13, 3]}, {"id": "17593", "type": [1, 0, 1, 0], "emotion": 4, "goal": [13]}, {"id": "16625", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "18356", "type": [1, 1, 0, 0], "emotion": 4, "goal": [13, 4]}, {"id": "7495", "type": [1, 0, 1, 0], "emotion": 0, "goal": [3, 11, 1, 0]}, {"id": "15981", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "13962", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 4, 15, 3]}, {"id": "12963", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 15, 10]}, {"id": "12210", "type": [1, 1, 0, 0], "emotion": 2, "goal": [6, 3]}, {"id": "15727", "type": [1, 0, 0, 0], "emotion": 4, "goal": [13, 3]}, {"id": "14806", "type": [1, 1, 0, 0], "emotion": 2, "goal": [4, 15]}, {"id": "18777", "type": [1, 1, 0, 0], "emotion": 4, "goal": [3, 16]}, {"id": "1972", "type": [1, 0, 1, 1], "emotion": 0, "goal": [0, 3, 11, 1]}]