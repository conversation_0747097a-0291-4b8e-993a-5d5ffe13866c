#!/usr/bin/env python3
"""
训练监控脚本 - 定期检查MISA和MULT的训练进展
"""

import time
import os
import re
from datetime import datetime

def check_training_progress():
    """检查训练进展"""
    print(f"\n{'='*60}")
    print(f"训练监控 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}")
    
    # 检查是否有输出目录
    output_dirs = ['outputs', 'results', 'models']
    for dir_name in output_dirs:
        if os.path.exists(dir_name):
            print(f"📁 {dir_name} 目录存在")
            files = os.listdir(dir_name)
            if files:
                print(f"   包含文件: {files[:5]}...")  # 只显示前5个文件
        else:
            print(f"📁 {dir_name} 目录不存在")
    
    # 检查日志文件
    log_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.log') or 'log' in file.lower():
                log_files.append(os.path.join(root, file))
    
    if log_files:
        print(f"\n📋 发现日志文件:")
        for log_file in log_files[:3]:  # 只显示前3个
            print(f"   {log_file}")
            try:
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    if lines:
                        print(f"      最后一行: {lines[-1].strip()}")
            except:
                print(f"      无法读取文件")
    
    # 检查模型文件
    model_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pth') or file.endswith('.pt') or file.endswith('.pkl'):
                model_files.append(os.path.join(root, file))
    
    if model_files:
        print(f"\n🤖 发现模型文件:")
        for model_file in model_files[:3]:  # 只显示前3个
            print(f"   {model_file}")
            try:
                size = os.path.getsize(model_file) / (1024*1024)  # MB
                print(f"      大小: {size:.1f} MB")
            except:
                print(f"      无法获取大小")
    
    print(f"\n{'='*60}")

if __name__ == "__main__":
    check_training_progress()
